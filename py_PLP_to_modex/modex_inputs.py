import xlwings as xw
import pandas as pd
import polars as pl
import numpy as np
from pathlib import Path

wb  = xw.Book.caller()
folder_plp = Path(wb.sheets('Control')['C4'].value)
folder_modex = Path(wb.sheets('Control')['C5'].value)

PLP = wb.sheets('Control')['C8'].value
modex = wb.sheets('Control')['C9'].value

def demanda_to_arreglo():
    """
    Transforma demanda formato modex a demanda anual.
    Luego, toma los factores de crecimiento del modelo para mostrar el total
    anual por agno-mes
    TODO: falta corregir el CRECIMIENTO de dda minera, ahora toma la ld
    """
    sh = wb.sheets('Dda')
    agno = str(int(sh['J1'].value))

    hojas = ['Demanda-R', 'Demanda-L', 'Demanda-LD']
    df_fin = {}
    for hoja in hojas:
        df = lee_dda_PLP_polars(str(folder_plp / PLP), hoja)
        df = sum_numeric_columns(df)
        df = df.select([pl.col(col).sum() for col in df.columns[2:]])
        df_fin[hoja] = df

    demanda = [df_fin[hojas[0]], df_fin[hojas[1]], df_fin[hojas[2]]]
    sun, mon, sat, wday = dias_agno(agno)

    i = 0
    do_columns = [col for col in df.columns if col.endswith('-DO')]
    lu_columns = [col for col in df.columns if col.endswith('-LU')]
    sa_columns = [col for col in df.columns if col.endswith('-SA')]
    tr_columns = [col for col in df.columns if col.endswith('-TR')]
    for arr in demanda:
        domingo = arr.with_columns([pl.col(col) * 2 for col in do_columns])
        lunes = arr.with_columns([pl.col(col) * 2 for col in lu_columns])
        sabado = arr.with_columns([pl.col(col) * 2 for col in sa_columns])
        dia_trabajo = arr.with_columns([pl.col(col) * 2 for col in tr_columns])

        total = (df.select(domingo).sum() +
                 df.select(lunes).sum() +
                 df.select(sabado).sum() +
                 df.select(dia_trabajo).sum()) / 1000

        sh['D70'].offset(0, i).options(
            index=True,
            expand='table',
            transpose=True).value = total.to_pandas()
        sh['U6'].offset(0, i).options(
            index=True,
            expand='table',
            transpose=True).value = dda_mensual.T.unstack().values
        i+=1
    return


def sum_numeric_columns(df, desde_columnas=2):
    """
    Convierte a float y suma las columnas desde la tercera en adelante.
    Las dos primeras columnas se mantienen como strings.
    """
    # Obtener nombres de todas las columnas
    todas_columnas = df.columns

    # Separar las columnas
    cols_str = todas_columnas[:desde_columnas]  # Primeras dos cols (strings)
    cols_num = todas_columnas[desde_columnas:]  # Resto de cols (numéricas)

    # Convertir las columnas numéricas a float y sumarlas
    df_convertido = df.with_columns(
        [pl.col(col).cast(pl.Float64) for col in cols_num])

    return df_convertido

def separa_dda_minera(df_dda_L, df_dda_Dx , dda_min):

    df1 = aplica_factor_dda(df_dda_L, dda_min, 'minero')
    df2 = aplica_factor_dda(df_dda_Dx, dda_min, 'dx')

    df_dda_L = df_dda_L.sum()
    df_dda_Dx = df_dda_Dx.sum()

    df_dda_L = df_dda_L - df1
    df_dda_Dx = df_dda_Dx - df2

    return df_dda_L, df_dda_Dx, df1, df2

def aplica_factor_dda(df_dda: pd.DataFrame, factor: pd.DataFrame,
                      tipo: str):
    """
    :df_dda: pandas DataFrame, Demanda formato modex que será multiplicada
        por el factor de dda minera en la barra
    :factor: pandas DataFrama, factor que se obtiene de la hoja "Barras"
        de modex, utilizado para multiplicar la demanda
    :tipo: puede ser dx, o minero que se refiere a minero en distribuvión,
        o minero
    :return: devuelve la demanda minera y la demanda libre descontada la
        minera por barra
    """
    df = df_dda.groupby(level=0).sum()
    df.reset_index(inplace=True)
    df = df.merge(factor, on='Barra', how='left')
    if tipo == 'dx':
        f_pomin = 'PorMinDx'
    else:
        f_pomin = 'PorMin'
    c_drop = ['Barra', 'PorMin', 'PorMinDx']
    df = df.drop(c_drop, axis=1).mul(df[f_pomin], axis=0)
    return df.sum()

def dias_agno(agno):
    """
    Función que devuelve la cantidad de días domingo, lunes, sábado y
    días de trabajo

    Args:
        agno (_type_): string de un entero igual a 1 año, ejemplo: '2021'

    Returns:
        devuelve el número de días correspondiente a :
                sun, mon, sat, wday
    """
    dia_i = [f'{agno}-01', f'{agno}-02', f'{agno}-03', f'{agno}-04',
             f'{agno}-05', f'{agno}-06', f'{agno}-07', f'{agno}-08',
             f'{agno}-09', f'{agno}-10', f'{agno}-11', f'{agno}-12']

    dia_f = [f'{agno}-02', f'{agno}-03', f'{agno}-04', f'{agno}-05',
             f'{agno}-06', f'{agno}-07', f'{agno}-08', f'{agno}-09',
             f'{agno}-10', f'{agno}-11', f'{agno}-12',
             f'{str(int(agno) + 1)}-01']

    sun = np.busday_count(dia_i, dia_f, weekmask='Sun')
    mon = np.busday_count(dia_i, dia_f, weekmask='Mon')
    sat = np.busday_count(dia_i, dia_f, weekmask='Sat')
    wday = np.busday_count(dia_i, dia_f, weekmask='0111100')

    return sun, mon, sat, wday

def ultima_linea_con_datos(df):
    """
    Filtra la data, deja afuera las lineas desde la primera fila 
    completa con Null 
    """
    row_w_nulls = df[df.isnull().all(axis=1) == True].index.tolist()
    if len(row_w_nulls) != 0:
        df = df.loc[0:row_w_nulls[0]-1]
    return df

def extrae_factores_dda(excel, cols):
    df = excel.parse('DemCre', usecols=cols, header=2, index_col=0).dropna()
    return df

def extrae_pmax(excel):
    hojas = ['CenHid', 'CenTer', 'CenRen', 'CenAcu']
    cols = [['Central', 'TipoCen', 'COD', 'Barra', 'PotMax'],
            ['Central', 'TipoCen', 'COD', 'Barra', 'PotMax'],
            ['Central', 'TipoCen', 'COD', 'Barra', 'PotMax'],
            ['Central', 'TipoCen', 'COD', 'Barra', 'PotDes']]

    df_fin = pd.DataFrame(columns=['Unidad', 'TipoCen', 'COD', 'Barra',
                                   'PotMax'])
    for h, c in zip(hojas, cols):
        # en casos antiguos "Central" en "CenTer" era "Unidad"
        try:
            df2 = excel.parse(sheet_name=h, usecols=c, header=2)
        except ValueError:
            c = ['Unidad', 'TipoCen', 'Barra', 'PotMax']
            df2 = excel.parse(sheet_name=h, usecols=c, header=2)
        df2 = ultima_linea_con_datos(df2)
        if 'Central' in df2.columns:
            df2.rename(columns={'Central':'Unidad'}, inplace=True)
        if 'PotDes' in df2.columns:
            df2.rename(columns={'PotDes':'PotMax'}, inplace=True)
        df_fin = pd.concat([df_fin, df2], join='outer')
    df_fin = df_fin.dropna()
    return df_fin

def trae_gen_modex():
    sh = wb.sheets('Gen_Modex')

    df = pd.ExcelFile(modex)
    df_fin = extrae_pmax(df)

    sh.range('B7').options(
        index=False,
        expand='table').value = df_fin

    df_fin['COD'] = df_fin['COD'].replace(r'^\s*$', 2022, regex=True)
    df_fin1 = df_fin.groupby(['COD', 'TipoCen'],
                            as_index=False).agg({'PotMax': sum})
    df_fin2 = df_fin1.pivot(columns='TipoCen', index='COD', values='PotMax')

    Tipos = {
        'HYDRO': ['pas', 'emb', 'reg'],
        'SOL': ['sol', 'sold'],
        'EOL': ['eol'],
        'OtrosERNC': ['acu', 'bio', 'cog', 'csp', 'geo'],
        'Term': ['car', 'die', 'fo', 'gas', 'glp']
    }
    df_agrupado = pd.DataFrame(
        {
            tipo: df_fin2[columnas].sum(axis=1) if tipo != 'Term' else
            df_fin2[columnas].sum(axis=1) - df_fin2['car'] for tipo,
        columnas in Tipos.items()}
    )
    sh.range('H7').options(
        index=True,
        expand='table').value = df_agrupado
    df_fin3 = df_fin.groupby(['Barra', 'TipoCen'],
                             as_index=False).agg({'PotMax': sum})
    df_fin3 = df_fin3.pivot(columns='TipoCen', index='Barra', values='PotMax')

    sh.range('AA7').options(
        index=True,
        expand='table').value = df_fin3

    return df_fin, df_fin2

def limpia_puntos_en_columnas(cols):
    if any("." in s for s in cols):
        cols = [s.split('.')[0] for s in cols]
    return cols

def trae_disp_gas():
    sh = wb.sheets('Gas')
    fi = sh['D1'].value
    # se le pone .2 a las columnas pues se repiten los nombres
    conver = {'AgnoIni.2':np.int32,'AgnoFin.2':np.int32,
              'MesIni.2':np.int32,'MesFin.2':np.int32}
    df = pd.read_excel(modex,
                       sheet_name='Combustibles',
                       usecols='X:AJ',
                       header=2,
                       converters=conver)
    if 'Tipo_Gas' in df.columns:
        df = df.loc[:, 'Tipo_Gas':]
        col = 'Tipo_Gas'
    else:
        df = df.loc[:, :'MaxMes']
        col = 'Conjunto'

    df.columns = limpia_puntos_en_columnas(df.columns)
    df['fechaIni'] = df.AgnoIni.astype(str) + '-' \
                   + df.MesIni.astype(str).str.zfill(2)
    df['fechaFin'] = df.AgnoFin.astype(str) + '-' \
                   + df.MesFin.astype(str).str.zfill(2)
    df = df[[col] + ['fechaIni', 'fechaFin', 'MinMes', 'MaxMes']]    
    df = df.groupby(['fechaIni', 'fechaFin', col],
                    as_index=False).agg({'MaxMes': 'sum'})

    ind = pd.date_range(str(int(fi)-1) + '-12',
                        df['fechaFin'].iloc[-1],
                        freq='MS')

    df2 = pd.DataFrame(index=ind, columns=df[col].unique())

    for r, c in df.iterrows():
        df2.loc[df.loc[r, 'fechaIni']:df.loc[r, 'fechaFin'],
                df.loc[r, col]] = df.loc[r, 'MaxMes']

    df2 = df2.loc[:, (df2.sum(axis=0) != 0)]
    df2 = df2[1:].fillna(0)
    sh.range('B7').options(
        index=True,
        expand='table').value = df2

    df2 = df2.groupby([df2.index.year]).mean()
    #sh['o1'].value = len(df2.columns)
    sh.range('B7').offset(0, len(df2.columns) + 4).options(
        index=True,
        expand='table').value = df2

def resumen_CodLimMant(df, fi, ff, tipo):
    df.columns = limpia_puntos_en_columnas(df.columns)

    old_new = 'Central' if 'Central' in df.columns else 'Unidad'
    if tipo=='COD':
        df['fecha'] = df.Agno.astype(str) + '-' \
                    + df.Mes.astype(str).str.zfill(2)
        df['PotMax'] = np.where(df['Tipo'] == 'F',
                                df['PotMax'] * -1,
                                df['PotMax'])
        ind = pd.date_range(str(int(fi)) + '-1',
                            str(int(ff)) + '-12', freq='MS')
        suma = {'PotMax': sum}
        grupo = ['TipoCen', 'fecha']
    else:
        df['fecha_i'] = df.AgnoIni.astype(str) + '-' \
                      + df.MesIni.astype(str).str.zfill(2)
        df['fecha_f'] = df.AgnoFin.astype(str) + '-' \
                      + df.MesFin.astype(str).str.zfill(2)
        ind = pd.date_range(str(int(fi)) + '-1',
                            str(int(ff)) + '-12', freq='MS')

        suma = {'PotMax': sum, 'PotMin':sum} if tipo=='Lim' \
            else {'Mdx_PotMin':sum, 'Mdx_PotMax':sum}
        potMax = 'PotMax' if tipo=='Lim' else 'Mdx_PotMax'
        potMin = 'PotMin' if tipo=='Lim' else 'Mdx_PotMin'
        grupo = [old_new, 'fecha_i', 'fecha_f']

    df = df.groupby(grupo, as_index=False).agg(suma)

    if tipo=='COD':
        df = df.pivot(index=['fecha'],
                      columns='TipoCen',
                      values='PotMax')
        df.index = pd.to_datetime(df.index)
        df2 = pd.DataFrame(index=ind, columns=df.columns)
        df2.update(df)
        return df2
    else:
        df2 = pd.DataFrame(index=ind, columns=df[old_new].unique())
        for r, c in df.iterrows():
            df2.loc[df.loc[r, 'fecha_i']:df.loc[r, 'fecha_f'],
                    df.loc[r, old_new]] = df.loc[r, potMax]
        dfmax = df2.loc[:, (df2.sum(axis=0) != 0)]
        df2 = pd.DataFrame(index=ind, columns=df[old_new].unique())
        for r, c in df.iterrows():
            df2.loc[df.loc[r, 'fecha_i']:df.loc[r, 'fecha_f'],
                    df.loc[r, old_new]] = df.loc[r, potMin]
        dfmin = df2.loc[:, (df2.sum(axis=0) != 0)]
        return dfmin, dfmax

def COD_Lim_Mant():
    sh = wb.sheets('CenDisp')
    sh['B7:AAA7000'].value = None
    fi, ff = sh['D1'].value, sh['D2'].value
    df = pd.ExcelFile(modex)
    cols = [['Central', 'Agno', 'Mes', 'Tipo'],
            ['Descripcion', 'Central', 'AgnoIni', 'MesIni', \
             'AgnoFin', 'MesFin', 'PotMin', 'PotMax'],
            ['Descripcion.1', 'Central.1', 'AgnoIni.1', 'MesIni.1', \
             'AgnoFin.1', 'MesFin.1', 'Mdx_PotMin', 'Mdx_PotMax', \
             'MdxHo_DiaIni', 'MdxHo_NumDias', 'MdxHo_PotMin', 'MdxHo_PotMax']]

    conver = {'Agno':np.int32,'Mes':np.int32,
              'AgnoIni':np.int32,'AgnoFin':np.int32, 
              'MesIni':np.int32,'MesFin':np.int32,
              'AgnoIni.1':np.int32,'AgnoFin.1':np.int32, 
              'MesIni.1':np.int32,'MesFin.1':np.int32}

    df = pd.ExcelFile(modex)
    df_gen = extrae_pmax(df)
    df1 = df.parse(sheet_name='CenDisp', header=2, converters=conver)

    # ********** COD
    df_cod = df1[cols[0]]
    df_cod = df_cod.merge(df_gen, on='Central', how='left')
    df_cod = ultima_linea_con_datos(df_cod)
    df_cod2 = resumen_CodLimMant(df_cod, fi, ff, 'COD')
    df_cod_gr = df_cod2.groupby([df_cod2.index.year]).sum()

    sh['B7'].options(index=True, expand='table').value = df_cod_gr
    df_cod2.index = df_cod2.index.strftime('%Y-%m')

    sh['B7'].offset(len(df_cod_gr) + 3,0).options(
        index=True,
        expand='table').value = df_cod2

    sh['B7'].offset(len(df_cod_gr) + len(df_cod2) + 6,0).options(
        index=False, 
        expand='table').value = df_cod[['Central', 'fecha', 'Tipo', 'TipoCen']]

    # ********** Limitaciones
    if 'Central' not in df1.columns:
        cols[1][1] = 'Unidad'
    df_lim = df1[cols[1]]
    df_lim = ultima_linea_con_datos(df_lim)
    df_lim_min, df_lim_max = resumen_CodLimMant(df_lim, fi, ff, 'Lim')

    sh['R7'].options(index=True, expand='table').value = df_lim_max
    sh['R7'].offset(len(df_lim_max) + 3,0).options(
        index=True, expand='table').value = df_lim_min

    cols_lim = len(df_lim_max.columns)
    # ********** Mantenimientos
    if 'Central' in df1.columns:
        df_man = df1[cols[2]]
        df_man = ultima_linea_con_datos(df_man)
        df_man_min, df_man_max = resumen_CodLimMant(df_man, fi, ff, 'Mant')

        sh['R7'].offset(0, cols_lim + 4).options(
            index=True,
            expand='table').value = df_man_max
        sh['BF7'].offset(len(df_man_max) + 3,cols_lim + 4).options(
            index=True,
            expand='table').value = df_man_min

def trae_combustible():
    sh = wb.sheets('Combustible')
    fi = sh['D1'].value

    cols = [['Combustible', 'Marcador', 'Factor', 'SumMul',
             'CostoPartida', 'CostoDetencion'],
            ['Combustible.1', 'Tipo', 'AgnoIni', 'MesIni', 'AgnoFin', 'MesFin',
             'Precio', 'Unidad'],
            ['Marcador', 'AgnoIni.1', 'MesIni.1', 'AgnoFin.1', 'MesFin.1',
             'Precio.1', 'Unidad.1']]

    conver = {'AgnoIni':np.int32, 'AgnoIni.1':np.int32,
              'AgnoFin':np.int32, 'AgnoFin.1':np.int32,
              'MesIni':np.int32,  'MesIni.1':np.int32,
              'MesFin':np.int32,  'MesFin.1':np.int32}

    sh_modex = pd.ExcelFile(modex)
    df = sh_modex.parse(sheet_name='Combustibles',
                        usecols='B:AA',
                        header=2)

    #************* Lista de Combustibles
    if 'Combustible' not in df.columns:
        df_old = sh_modex.parse(sheet_name='CenTer',
                                usecols='B:W',
                                header=2,
                                converters=conver)
        cols[0] = ['Unidad', 'Marcador', 'Factor', 'SumMul',
                   'CosPart', 'CosDete']
        df_lcomb = df_old[cols[0]]
    else:
        df_lcomb = df[cols[0]]
    df_lcomb.columns = limpia_puntos_en_columnas(df_lcomb.columns)
    df_lcomb = ultima_linea_con_datos(df_lcomb)
    sh.range('B7').options(index=False, expand='table').value = df_lcomb

    #************* Precios CEN
    if 'Combustible' not in df.columns:
        old = 'Central'
        cols[1][0] = old
    else:
        old = 'Combustible'
    
    df_precio = df[cols[1]]
    df_precio.columns = limpia_puntos_en_columnas(df_precio.columns)
    df_precio = ultima_linea_con_datos(df_precio)

    df_precio['fecha_i'] = df_precio.AgnoIni.astype(str) + '-' \
                         + df_precio.MesIni.astype(str).str.zfill(2)
    df_precio['fecha_f'] = df_precio.AgnoFin.astype(str) + '-' \
                         + df_precio.MesFin.astype(str).str.zfill(2)
    ind = pd.date_range(str(int(fi)) + '-1',
                        str(df_precio['AgnoFin'].max()) + '-12', freq='MS')

    df_p1 = df_precio.groupby(['Tipo', 'fecha_i', 'fecha_f'], 
                               as_index=False).agg({'Precio':np.mean})

    df_p1 = df_p1.pivot_table(index='Tipo', columns='fecha_i', values='Precio')
    df_p1 = df_p1.loc[(df_p1.sum(axis=1) != 0), :]
    sh.range('k7').options(index=True, expand='table').value = df_p1

    df_precio = df_precio.groupby([old, 'Tipo', 'fecha_i', 'fecha_f'], 
                                  as_index=False).agg({'Precio':np.mean})
    df_precio = df_precio.pivot_table(index=['Tipo', old],
                                      columns='fecha_i',
                                      values='Precio')
    df_precio = df_precio.loc[(df_precio.sum(axis=1) != 0), :]
    sh.range('J7').offset(len(df_p1)+3, 0).options(
        index=True, expand='table').value = df_precio


def aplica_factor_dda(df_dda: pd.DataFrame, factor: pd.DataFrame,
                      tipo: str):
    """
    :df_dda: pandas DataFrame, Demanda formato modex que será multiplicada
        por el factor de dda minera en la barra
    :factor: pandas DataFrama, factor que se obtiene de la hoja "Barras"
        de modex, utilizado para multiplicar la demanda
    :tipo: puede ser dx, o minero que se refiere a minero en distribuvión,
        o minero
    :return: devuelve la demanda minera y la demanda libre descontada la
        minera por barra
    """
    df = df_dda.groupby(level=0).sum()
    df.reset_index(inplace=True)
    df = df.merge(factor, on='Barra', how='left')
    if tipo == 'dx':
        f_pomin = 'PorMinDx'
    else:
        f_pomin = 'PorMin'
    c_drop = ['Barra', 'PorMin', 'PorMinDx']
    df = df.drop(c_drop, axis=1).mul(df[f_pomin], axis=0)
    return df.sum()

def handle_empty_values(df, columns=None):
    """
    Maneja diferentes tipos de valores vacíos (" ", "", null) en un DataFrame de Polars
    y los reemplaza con forward fill.

    Args:
        df: DataFrame de Polars
        columns: Lista de columnas a procesar. Si es None, procesa todas las columnas.
    """
    if columns is None:
        columns = df.columns

    return df.with_columns([
        pl.col(col).map_elements(
            lambda x: None if x in [" ", ""] else x,
            return_dtype=df.schema[col]
        ).fill_null(strategy='forward') for col in columns])

def lee_dda_PLP_polars(file_path, hoja_dda):
    dda_r = pl.read_excel(file_path, sheet_name=hoja_dda,
        read_options={"skip_rows": 4})

    # Extraer los nombres de las columnas de la primera fila
    original_columns = dda_r.row(0)
    new_columns = ["Barra", "Hora"]
    month_counter = 1
    for col in original_columns[2:]:
        if col in ["DO", "LU", "SA", "TR"]:
            new_columns.append(f"{month_counter}-{col}")
            if len(new_columns) % 4 == 2:  # 2 porque ya tenemos "Barra" y "Hora"
                month_counter += 1
        else:
            new_columns.append(col)  # Mantener otras columnas sin cambios

    # reemplaza columna, y elimina priemra fila
    dda_r = dda_r.rename(dict(zip(dda_r.columns, new_columns)))
    dda_r = dda_r.slice(1, len(dda_r) - 1)

    # Rellenar valores vacíos en la columna "Barra" hacia adelante (ffill)
    dda_r = handle_empty_values(dda_r)
    return dda_r

#%%
if __name__ == "__main__":
    #%%
    import pandas as pd
    import polars as pl
    import numpy as np
    from pathlib import Path

    def handle_empty_values(df, columns=None):
        """
        Maneja diferentes tipos de valores vacíos (" ", "", null) en un DataFrame de Polars
        y los reemplaza con forward fill.

        Args:
            df: DataFrame de Polars
            columns: Lista de columnas a procesar. Si es None, procesa todas las columnas.
        """
        if columns is None:
            columns = df.columns

        return df.with_columns([
            pl.col(col).map_elements(lambda x: None if x in [" ", ""] else x,
                return_dtype=df.schema[col]).fill_null(strategy='forward') for
            col in columns])

    def lee_dda_PLP_polars(file_path, hoja_dda):
        dda_r = pl.read_excel(file_path, sheet_name=hoja_dda,
                              read_options={"skip_rows": 4})

        # Extraer los nombres de las columnas de la primera fila
        original_columns = dda_r.row(0)
        new_columns = ["Barra", "Hora"]
        month_counter = 1
        for col in original_columns[2:]:
            if col in ["DO", "LU", "SA", "TR"]:
                new_columns.append(f"{month_counter}-{col}")
                if len(new_columns) % 4 == 2:  # 2 porque ya tenemos "Barra" y "Hora"
                    month_counter += 1
            else:
                new_columns.append(col)  # Mantener otras columnas sin cambios

        # reemplaza columna, y elimina priemra fila
        dda_r = dda_r.rename(dict(zip(dda_r.columns, new_columns)))
        dda_r = dda_r.slice(1, len(dda_r) - 1)

        # Rellenar valores vacíos en la columna "Barra" hacia adelante (ffill)
        dda_r = handle_empty_values(dda_r)
        return dda_r

    def sum_numeric_columns(df, desde_columnas=2):
        """
        Convierte a float y suma las columnas desde la tercera en adelante.
        Las dos primeras columnas se mantienen como strings.
        """
        # Obtener nombres de todas las columnas
        todas_columnas = df.columns

        # Separar las columnas
        cols_str = todas_columnas[:desde_columnas]  # Primeras dos columnas (strings)
        cols_num = todas_columnas[desde_columnas:]  # Resto de columnas (numéricas)

        # Convertir las columnas numéricas a float y sumarlas
        df_convertido = df.with_columns(
            [pl.col(col).cast(pl.Float64) for col in cols_num])

        return df_convertido

    def dias_agno(agno):
        """
        Función que devuelve la cantidad de días domingo, lunes, sábado y
        días de trabajo

        Args:
            agno (_type_): string de un entero igual a 1 año, ejemplo: '2021'

        Returns:
            devuelve el número de días correspondiente a :
                    sun, mon, sat, wday
        """
        dia_i = [f'{agno}-01', f'{agno}-02', f'{agno}-03', f'{agno}-04',
                 f'{agno}-05', f'{agno}-06', f'{agno}-07', f'{agno}-08',
                 f'{agno}-09', f'{agno}-10', f'{agno}-11', f'{agno}-12']

        dia_f = [f'{agno}-02', f'{agno}-03', f'{agno}-04', f'{agno}-05',
                 f'{agno}-06', f'{agno}-07', f'{agno}-08', f'{agno}-09',
                 f'{agno}-10', f'{agno}-11', f'{agno}-12',
                 f'{str(int(agno) + 1)}-01']

        sun = np.busday_count(dia_i, dia_f, weekmask='Sun')
        mon = np.busday_count(dia_i, dia_f, weekmask='Mon')
        sat = np.busday_count(dia_i, dia_f, weekmask='Sat')
        wday = np.busday_count(dia_i, dia_f, weekmask='0111100')

        return sun, mon, sat, wday

    folder_plp = Path(r'C:\Users\<USER>\OneDrive - Grupo CGE\2025\01_Modelacion_Modex\Caso2502\IPLP\PLP20240101_5anos-1')
    file = 'IPLP20240101.xlsm'
    agno = '2023'
    #%%

    hojas = ['Demanda-R', 'Demanda-L', 'Demanda-LD']
    df_fin = {}
    for hoja in hojas:
        df = lee_dda_PLP_polars(folder_plp / file, hoja)
        df = sum_numeric_columns(df)
        df = df.select([pl.col(col).sum() for col in df.columns[2:]])
        df_fin[hoja] = df
    #%%
    demanda = [df_fin[hojas[0]], df_fin[hojas[1]], df_fin[hojas[2]]]
    sun, mon, sat, wday = dias_agno(agno)

    i = 0
    do_columns = [col for col in df.columns if col.endswith('-DO')]
    lu_columns = [col for col in df.columns if col.endswith('-LU')]
    sa_columns = [col for col in df.columns if col.endswith('-SA')]
    tr_columns = [col for col in df.columns if col.endswith('-TR')]

    for arr in demanda:
        domingo = arr.with_columns([pl.col(col) * 2 for col in do_columns])
        lunes = arr.with_columns([pl.col(col) * 2 for col in lu_columns])
        sabado = arr.with_columns([pl.col(col) * 2 for col in sa_columns])
        dia_trabajo = arr.with_columns([pl.col(col) * 2 for col in tr_columns])

        total = (
            df.select(domingo).sum() +
            df.select(lunes).sum() +
            df.select(sabado).sum() +
            df.select(dia_trabajo).sum()
        ) / 1000
    #%%
    total
    #%%
    do_columns