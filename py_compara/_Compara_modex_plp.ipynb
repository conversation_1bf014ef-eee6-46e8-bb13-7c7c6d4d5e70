#%%
import pandas as pd
import numpy as np
from pathlib import Path
from py_compara.Compara_modex_plp import Compara
#%% md
# Archivos para análisis PLP y MODEX
#%%
# Definir Variables globales
año       = 2023
año_f     = 2032
hi_plp    = 52  # 37 o 47
hf_plp    = 61
carp_mod  = r'C:\_Modelo\2023\03_Marzo\03_v2'
caso      = '03_v2'
ar_modex  = '03_v2.xlsx'
carp_plp  = r'C:\CGE_py\py_PLP\Funcionando_p2\_5años_jun'
carpeta_F = '_Salidas_Ene23_v4'
#%%
asd  = Compara(carp_mod, carp_plp, carpeta_F, ar_modex, año, año_final=año_f)
gen  = True
comb = True
cmg  = True
tx   = False
cmg_hor = False
d_gas = True

# ****************    Procesa GENERACIÓN
if gen:
    df_m = asd.rgen_m(todos_esc=False)

# ****************    Procesa COMBUSTIBLE
if comb:
    df_cmod = asd.comb_modex(todos_los_años=2)
    df_cmod2 = asd.comb_modex(todos_los_años=2, cv_pc='pc')

# ****************    Procesa COSTO MARGINAL
if cmg:    
    df_cmgmod = asd.cmg_modex(NEsc=10)

if tx: 
    tx = asd.transmision(horario=0)
if cmg_hor:
    # hoja --> nombre de hoja en el excel a escribir
    # s_row/s_col --> para poner fila y columna en el excel a escribir
    cmg_hor = asd.cmg_modex_hor(hoja=caso)    
if d_gas:
    d_gas = asd.disp_gas()

#%%
carp_mod_1 = Path(r'C:\_Modelo\_Caso Octubre 2022\_Caso_2022_10_28')
carp_mod_2 = Path(r'C:\_Modelo\_Caso_Agosto_2022\_MODEX 09_24')
cmg        = 'TOHCMg.parquet'
df1 = pd.read_parquet(carp_mod_1 / cmg)
df2 = pd.read_parquet(carp_mod_2 / cmg)

df1 = df1.groupby(['Agno', 'Mes', 'Barra', 'Hora'], as_index=False).agg({'CMg': np.mean})
df2 = df2.groupby(['Agno', 'Mes', 'Barra', 'Hora'], as_index=False).agg({'CMg': np.mean})
df = df1.merge(df2, on=['Agno', 'Mes', 'Barra', 'Hora'], how='left')

df_sol = df.loc[df['Barra'].str.contains('Quillota220')]
df_sol['fecha'] = df_sol.Agno.astype(str) + '-' \
                     + df_sol.Mes.astype(str).str.zfill(2)
df_sol = df_sol[['fecha', 'Barra', 'Hora', 'CMg_x', 'CMg_y']]

df_sol.to_excel('compara_quillota220.xlsx')
#%%
# Revisar Mantenimientos o combustibles de formato PLP
carpeta = Path(r'C:\_Modelo\2023\01_Enero\plp')
archivo = 'IPLP20230110.xlsm'
hoja    = 'Disp. Combustibles(2)'
print(f'Abriendo archivo: {archivo}')
df = pd.read_excel(carpeta/ archivo,
                   sheet_name=hoja, usecols='B:F', header=4)
df.dropna(inplace=True)

dates = pd.date_range('2022-1', end='2032-1', freq='M')
df2 = pd.DataFrame(columns=df.CENTRAL.unique(), index=dates)

df3 = df2.copy()
largo = len(df3.columns)
posicion = 1

df['INICIAL'] = pd.to_datetime(
    dict(year=df.INICIAL.dt.year,
         month=df.INICIAL.dt.month,
         day=df.INICIAL.dt.day))
df['FINAL'] = pd.to_datetime(
    dict(year=df.FINAL.dt.year,
         month=df.FINAL.dt.month,
         day=df.FINAL.dt.day))
for unidad, v in df2.iteritems():
    print(f'Progreso: {round((posicion / largo*100), 2)}%', end='\r')
    df_aux = df.loc[df.CENTRAL == unidad]
    s = pd.Series(data=df_aux['MÁXIMA'].values,
                  index=pd.IntervalIndex.from_arrays(df_aux['INICIAL'],
                                                     df_aux['FINAL'],
                                                     closed='both'))
    try:
        df3[unidad] = df2.index.map(s)
    except:
        for ind in range(len(df_aux)):
            s = pd.Series(data=df_aux.iloc[ind, -1],
                          index=pd.interval_range(df_aux.iloc[ind]['INICIAL'],
                                                  df_aux.iloc[ind]['FINAL'],
                                                  closed='left'))
            df3[unidad] = df2.index.map(s)
            df3.loc[df_aux.iloc[ind, 3], unidad] = df_aux.iloc[ind, -1]
            df2 = df3.combine_first(df2)
    posicion +=1
df2 = df3.combine_first(df2)
df2['total'] = df2.sum(axis=1)
df2
#%%
df2.to_excel(carpeta/ 'combustibles.xlsx', index=True)
#%%
carp_mod  = Path(r'C:\_Modelo\_Caso_Agosto_2022\_MODEX 09_24')

df = pd.DataFrame(columns=[
    'Dia', 'Bloque', 'Escenario', 'Central', 
    'Tipo', 'Agno', 'Mes', 'Hora', 'Energia'])
archivos_gen = ['TOHGenHid.parquet', 'TOHGenTer.parquet',
                'TOHGenRen.parquet', 'TOHGenAcu.parquet']
for ar in archivos_gen:
    df_aux = pd.read_parquet(carp_mod / ar)
    df = pd.concat([df, df_aux], join="inner")

df.to_parquet(carp_mod / 'gen_total_h.parquet')
#%%
ar_modex  = 'gen_total_h.parquet'
df = pd.read_parquet(carp_mod / ar_modex)
res = df.groupby(['Agno', 'Hora', 'Tipo']).agg({'Energia': np.sum})
res = res.reset_index()
res = res.pivot_table(columns='Tipo', values='Energia', index=['Agno', 'Hora'])
res.to_excel(carp_mod / 'gen_horaria.xlsx')

#%%
