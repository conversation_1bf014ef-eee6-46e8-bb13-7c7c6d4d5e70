#%%
import pandas as pd
import numpy as np
from Compara_modex_plp_v2 import ModexHor
from pathlib import Path
import matplotlib.pyplot as plt
from matplotlib.lines import Line2D


def grafica_max_min(folder_out, df_max_min, barra, hor_men):
    savefig = folder_out / f'grafo_{hor_men}.png'
    data = df_max_min.copy()
    x = data.index
    df_promedio = data['Mean']
    df_maximo = data.iloc[:, -3]
    df_minimo = data.iloc[:, -2]
    fz = 16

    fig, ax = plt.subplots(figsize=(21, 5.5))
    ax.set_title(
        f'Costo Marginal: {barra} [USD/MWh]',
        fontsize=22,
        color='royalblue',
        fontweight='bold'
    )
    df_line = df_promedio.plot(ax=ax, linewidth=2)

    ax.fill_between(range(0, len(x.values)),
                    df_minimo.values,
                    df_maximo.values, alpha=0.2)

    # Ocultar los bordes superior y derecho de los ejes
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)

    # Ocultar las marcas de los ejes en los bordes superior y derecho
    ax.tick_params(axis='both', which='both', top=False, right=False)

    # Configurar los límites del eje x
    ax.set_ylabel("[USD/MWh]", rotation=0, ha='right', va='center',
                  fontsize=fz)
    ax.yaxis.set_label_coords(0.05, 1.07)

    # Guardar el gráfico
    fig.subplots_adjust(top=1, left=0, bottom=0.1, right=0.95)
    fig.savefig(savefig, dpi=500, bbox_inches='tight')

def mean_max_min_mensual(folder_path, barra, savefig=True):
    carp = Path(folder_path)
    parq = 'TOCMG.parquet'

    par = pd.read_parquet(carp / parq)
    pol = par.loc[par['Barra'].str.contains(barra)]

    df = pol.copy()
    df.loc[:, 'd_cmg'] = df.loc[:, 'Horas'] * df.loc[:, 'CMg']
    df = df.groupby(['Agno', 'Mes', 'Escenario', 'Barra'], as_index=False
                    ).agg({'d_cmg': np.sum, 'Horas': np.sum})
    df['CMg'] = df['d_cmg'] / df['Horas']

    agno_mes = df.groupby(['Escenario', 'Agno', 'Mes'], as_index=False).agg(
        {'CMg': np.mean}
    )

    agno_mes = agno_mes.pivot_table(
        columns='Escenario',
        index=['Agno', 'Mes'],
        values='CMg')

    agno_mes['Mean'] = agno_mes.mean(axis=1)

    mean_row = pd.DataFrame(agno_mes.mean(axis=0), columns=['Promedio'])
    max_index = mean_row.idxmax()
    min_index = mean_row.idxmin()
    agno_mes[f'{max_index.item()}_Max'] = agno_mes[max_index]
    agno_mes[f'{min_index.item()}_Min'] = agno_mes[min_index]
    agno_mes['Std'] = agno_mes.std(axis=1)
    agno_mes.reset_index(inplace=True)
    agno_mes.to_excel(carp / 'agno_mes.xlsx', index=False)

    if savefig:
        grafica_max_min(carp, agno_mes, barra, 'mensual')
    return agno_mes

def mean_max_min_horario(folder_path, barra, savefig=True):
    carp = Path(folder_path)
    parq = 'TOHCMg.parquet'

    par = pd.read_parquet(carp / parq)
    pol = par.loc[par['Barra'].str.contains(barra)]

    df = pol.copy()
    df.loc[:, 'd_cmg'] = df.loc[:, 'No_Dias'] * df.loc[:, 'CMg']
    df = df.groupby(['Agno', 'Mes', 'Escenario', 'Barra', 'Hora'],
                    as_index=False).agg({'d_cmg': np.sum, 'No_Dias': np.sum})
    df['CMg'] = df['d_cmg'] / df['No_Dias']

    agno_mes = df.groupby(['Escenario', 'Agno', 'Mes'],
                          as_index=False).agg({'CMg': np.mean})
    agno_mes = agno_mes.pivot_table(columns='Escenario',
                                    index=['Agno', 'Mes'],
                                    values='CMg')
    agno_mes['Mean'] = agno_mes.mean(axis=1)

    mean_row = pd.DataFrame(agno_mes.mean(axis=0), columns=['Promedio'])
    max_index = mean_row.idxmax()
    min_index = mean_row.idxmin()
    agno_mes[f'{max_index.item()}_Max'] = agno_mes[max_index]
    agno_mes[f'{min_index.item()}_Min'] = agno_mes[min_index]
    agno_mes['Std'] = agno_mes.std(axis=1)
    agno_mes.reset_index(inplace=True)
    agno_mes.to_excel(carp / 'agno_mes_horario.xlsx', index=False)

    if savefig:
        grafica_max_min(carp, agno_mes, barra, 'horario')
    return agno_mes


#%%
if __name__ == '__main__':
    carpeta = r'C:\_Modelo\2023\07_Julio\07_v3'
    barra = 'Polpaico220'
    df = mean_max_min_mensual(carpeta, barra)
    df = mean_max_min_horario(carpeta, barra)

    #%%
    file = r'C:\_Modelo\2023\03_Marzo\20221229_ProyeccCMg_SENSIB_CGE_V2.xlsx'
    df = pd.read_excel(file, sheet_name='CMg_serie', header=4,
                       usecols='A:D, BP:CT')
    dfH = pd.read_excel(file, sheet_name='Representación_bloque', header=4,
                        index_col=0, usecols='B:Z')

    dfN = dfH.iloc[17:].dropna().reset_index().drop(columns='index')
    dfH = dfH.iloc[:16].dropna().reset_index().drop(columns='index')
    dfH.index = dfH.index +1
    dfN.index = dfN.index +1

    dfN = dfN.unstack().reset_index().rename(
        columns={'level_0': 'H1', 'level_1': 'mes', 0: 'val'})
    dfH = dfH.unstack().reset_index().rename(
        columns={'level_0': 'H2', 'level_1': 'mes', 0: 'val'})
    #%%
    df_fin = df.merge(dfN, how='left', left_on=['BL', 'mes'],
                      right_on=['val', 'mes'])
    df_fin = df_fin.merge(dfH, how='left', left_on=['BL', 'mes'],
                          right_on=['val', 'mes'])

    df_fin = df_fin.loc[df_fin['Año'] <= 2032]
    df_fin.fillna(0, inplace=True)
    df_fin['H'] = df_fin['H1'] + df_fin['H2']
    df_fin.drop(columns=['val_x', 'val_y', 'H1', 'H2'], inplace=True)
    #%%
    df_hora = df_fin.groupby(['Año', 'H'], as_index=False).mean()
    #df_fin.to_excel(r'C:\_Modelo\2023\03_Marzo\valgesta_h.xlsx')
    #%%
    df_hora.to_excel(r'C:\_Modelo\2023\03_Marzo\valgesta_h.xlsx')
    #%%
    df_mes = df_fin.groupby(['Año', 'mes'], as_index=False).mean(numeric_only=True)
    #%%
    df_mes.to_excel(r'C:\_Modelo\2023\03_Marzo\valgesta_mes.xlsx')
