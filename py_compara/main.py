#%%
import Compara_modex_plp_v2 as cmod
from pathlib import Path
import glob
import pandas as pd

if __name__ == "__main__":
    carp = Path(r'C:\_Modelo\2023\07_Julio\07_v3')
    ar_mod = '07_v3.xlsx'
    prefijo = '_salida_'
    cmg_csv  = False
    cmg_hor  = True
    cmg_men  = True
    gen      = False
    comb     = False
    transmi  = False
    disp_gas = True
    demanda  = False
    junta_excel = True

    if cmg_csv:
        print('\n\t---------- Proceso CMg CSV')
        csv_files = glob.glob(str(carp / '*.csv'))
        cols_merge = ['Barra', 'Latitud', 'Hora']
        df = pd.DataFrame(columns=cols_merge)
        for csv in csv_files:
            mod = cmod.ModexHor(carp / csv)
            df_aux = mod.open_file()
            df_aux = mod.calc_mean(df_aux)
            df = df.merge(df_aux, on=cols_merge, how='outer')

        save_file = carp / f'{prefijo}HCMg_csv.xlsx'
        mod.save_file(df, save_file)
        print(f'File saved: {save_file}')
    if cmg_men:
        print('\n\t---------- Proceso CMg MENSUAL')
        archivo = 'TOCMg.parquet'
        mod = cmod.ModexMensual(carp / archivo)
        df = mod.open_file()
        df = mod.calc_mean(df)

        save_file = carp / f'{prefijo}CMg.xlsx'
        mod.save_file(df, save_file)
        print(f'File saved: {save_file}')
    if cmg_hor:
        print('\n\t---------- Proceso CMg HORARIO')
        archivo = 'TOHCMg.parquet'
        mod = cmod.ModexHor(carp / archivo)
        df = mod.open_file()
        df = mod.calc_mean(df)

        save_file = carp / f'{prefijo}HCMg.xlsx'
        mod.save_file(df, save_file)
        print(f'File saved: {save_file}')
    if gen:
        print('\n\t---------- Proceso GENERACION')
        archivos_gen = ['TOGenTer.parquet', 'TOGenHid.parquet',
                        'TOGenRen.parquet', 'TOGenAcu.parquet']

        df = pd.DataFrame()
        for archivo in archivos_gen:
            mod = cmod.ModexMensual(carp / archivo)
            df_aux = mod.open_file()
            df_aux = mod.calc_mean(df_aux)
            df = pd.concat([df, df_aux])

        save_file = carp / f'{prefijo}Gen.xlsx'
        mod.save_file(df, save_file)
        print(f'File saved: {save_file}')
    if comb:
        print('\n\t---------- Proceso Combustible')
        archivo = 'TOGenTer.parquet'
        mod = cmod.ModexMensual(carp / archivo)
        df = mod.open_file()
        precio_comb, costo_var = mod.combustible(df)

        save_file1 = carp / f'{prefijo}PrecioComb.xlsx'
        save_file2 = carp / f'{prefijo}CVar.xlsx'
        mod.save_file(precio_comb, save_file1)
        print(f'File saved: {save_file1}')
        mod.save_file(costo_var, save_file2)
        print(f'File saved: {save_file2}')
    if transmi:
        print('\n\t---------- Proceso Transmisión')
        archivo = 'TOTran.parquet'
        mod = cmod.ModexMensual(carp / archivo)
        df = mod.open_file()
        df = mod.transmision(df, 0.95)

        save_file = carp / f'{prefijo}Transmision.xlsx'
        mod.save_file(df, save_file)
        print(f'File saved: {save_file}')
    if disp_gas:
        print('\n\t---------- Proceso Disponibilidad de Gas')
        mod = cmod.ModexMensual(carp / ar_mod)
        df = mod.open_file()
        df = mod.disp_gas(df)

        save_file = carp / f'{prefijo}DipComb.xlsx'
        mod.save_file(df, save_file)
        print(f'File saved: {save_file}')
    if demanda:
        print('\n\t---------- Proceso Demanda')
        mod = cmod.ModexMensual(carp / ar_mod)
        df = mod.open_file()
        anual, mensual = mod.demand(df, 2021)

        save_file1 = carp / f'{prefijo}dda_Anual.xlsx'
        save_file2 = carp / f'{prefijo}dda_Mensual.xlsx'
        mod.save_file(anual, save_file1, index=True)
        print(f'File saved: {save_file1}')
        mod.save_file(mensual, save_file2, index=True)
        print(f'File saved: {save_file2}')
    if junta_excel:
        print('\n\t---------- Proceso Junta Salidas')
        mod = cmod.ModexMensual(carp / ar_mod)
        mod.join_excel_files('_salida_', drop_file=True)
        print('Proceso Terminado')

