#%%
import pandas as pd
import numpy as np
from Compara_modex_plp_v2 import ModexHor
from pathlib import Path


carpeta = Path(r'C:\_Modelo\2023\07_Julio\07_v3')
barra = 'Polpaico220'

def cmg_med_x_bloque(df_cmg):
    df = df_cmg.copy()
    df.loc[:, 'd_cmg'] = df_cmg.loc[:, 'Horas'] * df_cmg.loc[:, 'CMg']

    df = df.groupby(
        ['Agno', 'Mes', 'Escenario', 'Bloque', 'Barra'], as_index=False
    ).agg({'d_cmg': np.sum, 'Horas': np.sum})
    df['CMg'] = df['d_cmg'] / df['Horas']

    df = df.groupby(['Agno', 'Mes', 'Barra', 'Bloque', 'Horas'],
                    as_index=False).agg(
        {'CMg': np.mean})
    return df

def gen_med_x_bloque(df_gen):
    df = df_gen.copy()
    df = df.groupby(
        ['Agno', 'Mes', 'Escenario', 'Bloque', 'Tipo'], as_index=False
    ).agg({'Energia': np.sum})
    df = df.groupby(['Agno', 'Mes', 'Tipo', 'Bloque'], as_index=False).agg(
        {'Energia': np.mean})
    return df

#%%
parq1 = 'TOCMG.parquet'
parq2 = 'TOGenRen.parquet'
barras = ['Lagunas220', 'Polpaico220', 'Charrua220', 'Parinas220']

par1 = pd.read_parquet(carpeta / parq1)
par2 = pd.read_parquet(carpeta / parq2)

# ********** Análisis CMg
df = cmg_med_x_bloque(par1.loc[par1['Barra'].isin(barras)])
df = df.pivot_table(columns='Barra',
                    index=['Agno', 'Mes', 'Bloque', 'Horas'],
                    values='CMg').reset_index()

# ********** Análisis Gen
df2 = gen_med_x_bloque(par2.loc[par2['Tipo'].isin(['sol', 'sold', 'eol'])])
df2 = df2.pivot_table(columns='Tipo',
                      index=['Agno', 'Mes', 'Bloque'],
                      values='Energia').reset_index()

# ********** Juntar y grabar
df3 = df.merge(df2)
df3.to_excel(carpeta / 'cmg_lcoe.xlsx', index=False)
#%%
lcoe_min = Path(r'C:\_Modelo\2023\05_Mayo\_inputs\Ministerio')
alto_min = r'Bajo_LCOE-Tecnologías de Generación_Datos completos_data.csv'
df = pd.read_csv(lcoe_min / alto_min, sep=';', decimal=',')
df = df.loc[df['Año de Año'].between(2023, 2032)]
df = df.loc[df['Tecnología1'].isin(['Eolica 100mts', 'Eolica 140mts', 'Solar FV'])]
df = df.sort_values(by=['Tecnología1', 'Año de Año'])
df.to_excel(carpeta / 'lcoe_ministerio_bajo.xlsx', index=False)

