import pandas as pd
import zipfile
from pathlib import Path

class OpenFile:
    """
    Clase para abrir distintos tipos de archivos de datos, para análisis
    utilizando pandas.
    """
    def __init__(self, file_name):
        self.file_name = file_name

    def file_exists(self):
        """
        :return: Devuelve si el archivo existe o no
        """
        return self.file_name.is_file()

    def open_file(self):
        """
        Revisa si el archivo es excel, parquet o csv,y abre el archivo
        dependiendo de su extensión
        :return: DataFrame, devuelve un dataframe con el contenido del archivo
        """
        if self.file_exists():
            if self.file_name.name.endswith('xlsx'):
                return pd.read_excel(self.file_name, sheet_name=None)
            if self.file_name.name.endswith('xlsm'):
                return pd.read_excel(self.file_name, sheet_name=None)
            if self.file_name.name.endswith('parquet'):
                return pd.read_parquet(self.file_name)
            if self.file_name.name.endswith('csv'):
                return pd.read_csv(self.file_name)
        else:
            raise FileNotFoundError(f"File doesn't exist \n\t{self.file_name}")

class SaveFile:
    """
    Graba el archivo dependendo de la extensión contenida en "name".
    Supone que la data es un Dataframe
    """
    def save_file(self, df, name, index=False):
        if isinstance(df, pd.DataFrame):
            if str(name).endswith('xlsx'):
                return df.to_excel(name, index=index)
            if str(name).endswith('parquet'):
                return df.to_parquet(name, index=index)
            if str(name).endswith('csv'):
                return df.to_csv(name, index=index, encoding='latin')

class JoinXlsxToOne:
    """
    Class to join many xlsx of some kind, to one final xlsx.
        prefix: Prefix of xlsx files to join
        final_name: Default '_Final_xl.xlsx'. Name of final xlsx.
    """

    def join_excel_files(self, prefix, final_name='_Final_xl.xlsx',
                         drop_file=False):
        folder = self.file_name.parent
        files_to_join = folder.glob(f'{prefix}*.xlsx')

        try:
            # load _final_xl.xlsx if it exists
            result = pd.read_excel(folder / final_name, sheet_name=None,
                                   engine='openpyxl')

            writer = pd.ExcelWriter(folder / final_name, engine='openpyxl',
                                    mode='a', if_sheet_exists='overlay')

        except FileNotFoundError:
            # create a excel writer if _final_xl.xlsx doesn't exist
            writer = pd.ExcelWriter(folder / final_name, engine='openpyxl')
            result = pd.DataFrame()

        for file in files_to_join:
            sh_fin = file.name.split('.xlsx')[0]
            print(f'Appending {file}')
            df = pd.read_excel(file)
            if sh_fin in result:
                result[sh_fin] = df
            else:
                df.to_excel(writer, sheet_name=sh_fin, index=False)
            if drop_file:
                file.unlink()
        writer.close()

class ReadZipfile:
    def __init__(self, file_name):
        self.file_name = Path(file_name)

    def read_zip_file(self, file='', where='', delete_zip=False):
        try:
            with zipfile.ZipFile(self.file_name, 'r') as azip:
                azip.extract(member=file, path=where)
                azip.close()

        except Exception as f_error:
            print(f'Algo ocurrió al extraer Zip: \n{f_error}')
        finally:
            zipfile.ZipFile(self.file_name, 'r').close()

        if delete_zip:
            self.file_name.unlink()

#%%
if __name__ == '__main__':
    file = Path(r'C:\_BD_Clientes\2023\CMg\cmg2106_pre.zip')
    nombre = file.name.split('.zip')[0] + '.xlsm'
    zip = ReadZipfile(file)
    zip.read_zip_file(file=nombre, where=file.parent, delete_zip=True)
