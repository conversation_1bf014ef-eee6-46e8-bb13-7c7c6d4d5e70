import pandas as pd
import numpy as np
from pathlib import Path
from file_man import OpenFile, SaveFile, JoinXlsxToOne


class Modex(OpenFile, SaveFile, JoinXlsxToOne):
    excel_mod = {
        'sh_disp_comb': 'Combustibles',
        'col_disp_comb': ['Tipo_Gas', 'AgnoIni.2', 'MesIni.2', 'AgnoFin.2',
                          'MesFin.2', 'MaxMes']
    }
    def __calc_med(self, df, col_val, col_dur, gr_cols, gr_fin):
        if col_dur:
            col_aux = 'val_x_duracion'
            df[col_aux] = df[col_val] * df[col_dur]
            df = df.groupby(gr_cols, as_index=False).agg({col_aux: np.sum,
                                                          col_dur: np.sum})
            df[col_val] = df[col_aux] / df[col_dur]
        else:
            df = df.groupby(gr_cols, as_index=False).agg({col_val: np.sum})

        df = df.groupby(gr_fin, as_index=False).agg({col_val: np.mean})
        return df

    def __cols_to_date(self, df, agno='Agno', mes='Mes'):
        df['fecha'] = df[agno].astype(str) + '-' \
                    + df[mes].astype(str).str.zfill(2)
        return df

    def __pivotea(self, df, columnas, valores, indice, orden=None):
        df = df.pivot_table(columns=columnas,
                            values=valores,
                            index=indice).reset_index()
        if orden:
            df = df.sort_values(orden, ascending=False)
        return df

    def limpia_puntos_en_columnas(self, cols):
        if any("." in s for s in cols):
            cols = [s.split('.')[0] for s in cols]
        return cols

    def dias_agno(self, agno):
        """
        Función que devuelve la cantidad de días domingo, lunes, sábado y
        días de trabajo

        Args:
            agno (_type_): string de un entero igual a 1 año, ejemplo: '2021'

        Returns:
            devuelve el número de días correspondiente a :
                    sun, mon, sat, wday
        """
        dia_i = [f'{agno}-01', f'{agno}-02', f'{agno}-03',
                 f'{agno}-04', f'{agno}-05', f'{agno}-06',
                 f'{agno}-07', f'{agno}-08', f'{agno}-09',
                 f'{agno}-10', f'{agno}-11', f'{agno}-12']

        dia_f = [f'{agno}-02', f'{agno}-03', f'{agno}-04',
                 f'{agno}-05', f'{agno}-06', f'{agno}-07',
                 f'{agno}-08', f'{agno}-09', f'{agno}-10',
                 f'{agno}-11', f'{agno}-12', f'{str(int(agno) + 1)}-01']

        sun = np.busday_count(dia_i, dia_f, weekmask='Sun')
        mon = np.busday_count(dia_i, dia_f, weekmask='Mon')
        sat = np.busday_count(dia_i, dia_f, weekmask='Sat')
        wday = np.busday_count(dia_i, dia_f, weekmask='0111100')

        return sun, mon, sat, wday

    def extrae_factores_dda(self, excel, cols):
        df = excel['DemCre'].loc[:, cols[0]:cols[1]].dropna()
        return df

    def disp_gas(self, df_excel):
        data = self.excel_mod
        df = df_excel[data['sh_disp_comb']][data['col_disp_comb']].copy()
        df.dropna(inplace=True)
        df.columns = self.limpia_puntos_en_columnas(df.columns)
        df.loc[:, 'INICIAL'] = pd.to_datetime(dict(year=df.loc[:, 'AgnoIni'],
                                            month=df.loc[:, 'MesIni'],
                                            day=1))
        df.loc[:, 'FINAL'] = pd.to_datetime(dict(year=df.loc[:, 'AgnoFin'],
                                                 month=df.loc[:, 'MesFin'],
                                          day=1)) + pd.offsets.MonthEnd(1)

        dates = pd.date_range(start=df['INICIAL'].min(),
                              end=df['FINAL'].max(), freq='M')
        cal = pd.DataFrame(columns=df.Tipo_Gas.unique(), index=dates)
        df3 = cal.copy()
        for unidad, v in cal.items():
            df_aux = df.loc[df['Tipo_Gas'] == unidad]
            s = pd.Series(data=df_aux['MaxMes'].values,
                          index=pd.IntervalIndex.from_arrays(df_aux['INICIAL'],
                                                             df_aux['FINAL'],
                                                             closed='both'))
            try:
                df3[unidad] = cal.index.map(s)
            except:
                for ind in range(len(df_aux)):
                    s = pd.Series(data=df_aux.iloc[ind, -1],
                                  index=pd.interval_range(df_aux.iloc[ind, 2],
                                                          df_aux.iloc[ind, 3],
                                                          closed='left'))
                    df3[unidad] = cal.index.map(s)
                    df3.loc[df_aux.iloc[ind, 3], unidad] = df_aux.iloc[ind, -1]
                    cal = df3.combine_first(cal)

        cal = df3.combine_first(cal)
        cal['Total_P'] = cal.sum(axis=1)
        cal['dias'] = cal.index.daysinmonth
        cal['Total_E'] = cal.dias * 24 * cal.Total_P / 1000
        cal.drop(columns=['dias'], inplace=True)
        print(f'Gas Disp. Process Finished: {self.file_name}')
        return cal.reset_index()

    def demand(self, df, agno_dda_real):
        """
           Transforma demanda formato modex a demanda anual.
           Luego, toma los factores de crecimiento del modelo para mostrar el total
           anual por agno-mes
           TODO: falta la demanda minera y minera distribución
       """
        df['DemCre'].columns = [str(x) for x in df['DemCre'].columns]
        dda_r = df['DemR'].loc[:, 'Barra':'12-TR'].copy().sum()
        dda_l = df['DemL'].loc[:, 'Barra':'12-TR'].copy().sum()
        dda_ld = df['DemLD'].loc[:, 'Barra':'12-TR'].copy().sum()

        demanda = [dda_r, dda_l, dda_ld]
        sun, mon, sat, wday = self.dias_agno(agno_dda_real)
        cols_factores = [['1', '12'], ['1.1', '12.1'], ['1.3', '12.3']]
        i = 0
        total_anual = []
        total_mensual = []
        for arr in demanda:
            domingo = arr.loc[arr.index.str.contains('DO')] * sun
            lunes = arr.loc[arr.index.str.contains('LU')] * mon
            sabado = arr.loc[arr.index.str.contains('SA')] * sat
            dia_trabajo = arr.loc[arr.index.str.contains('TR')] * wday

            total = (domingo.values + lunes.values \
                     + sabado.values + dia_trabajo.values) / 1000

            total_anual.append(total)
            dda_mensual = total * self.extrae_factores_dda(df,
                                                           cols_factores[i])
            dda_mensual = dda_mensual.T.unstack().values
            total_mensual.append(dda_mensual)
            i += 1

        total_yr = pd.DataFrame(total_anual).T
        dates = pd.date_range(
            start=f'{agno_dda_real}-01',
            periods=12,
            freq='M')
        total_yr.set_index(dates, inplace=True)
        total_yr.columns = ['R', 'L', 'LD']

        total_men = pd.DataFrame(total_mensual).T
        dates = pd.date_range(
            start=f'{agno_dda_real+1}-01',
            periods=len(total_men),
            freq='M')
        total_men.set_index(dates, inplace=True)
        total_men.columns = ['R', 'L', 'LD']

        return total_yr, total_men

    def filtra_df(self, df, filtro):
        if isinstance(filtro, str):
            filtro = [filtro]
        if 'CMg'.lower() in str(self.file_name).lower():
            columna = 'Barra'
        elif 'Gen'.lower() in str(self.file_name).lower():
            columna = 'Central'
        elif 'Tras'.lower() in str(self.file_name).lower():
            columna = 'Linea'

        return df.loc[df[columna].str.lower().str.contains('|'.join([
            x.lower() for x in filtro]), regex=False)]


class ModexHor(Modex):
    """
    # TODO: Falta más documentacion
    Clase para manejar archivos MODEX y Modex Hor
    Parameters:
        file_name: nombre del archivo, con path incluido

    Methods:
        calc_mean: calcular el promedio ponderado de los datos
    """
    data_cmg ={
        'col_val': 'CMg',
        'gr_cols': ['Escenario', 'Barra', 'Agno', 'Mes', 'Hora', 'Latitud'],
        'gr_fin':  ['Barra', 'Agno', 'Mes', 'Hora', 'Latitud'],
        'piv_ind': ['Barra', 'Latitud', 'Hora'],
        'sort_or': ['Latitud', 'Barra'],
        'col_dur': 'No_Dias'
    }
    def __init__(self, file_name):
        self.file_name = Path(file_name)
        self.carpeta_F = self.file_name.parent

    def calc_mean(self, df, txt=True):
        if 'CMg'.lower() in self.file_name.name.lower():
            if 'TOHCMg'.lower() not in self.file_name.name.lower():
                raise ValueError(f'File {self.file_name.name} must be HORARIO')
            df = self._Modex__calc_med(
                df,
                col_val=self.data_cmg['col_val'],
                col_dur=self.data_cmg['col_dur'],
                gr_cols=self.data_cmg['gr_cols'],
                gr_fin=self.data_cmg['gr_fin'])
            self._Modex__cols_to_date(df)
            df = self._Modex__pivotea(
                df, 'fecha', self.data_cmg['col_val'],
                self.data_cmg['piv_ind'], self.data_cmg['sort_or'])
        if txt:
            print(f'Mean Calculation Process Finished: {self.file_name}')
        return df


class ModexMensual(Modex):
    data_cmg  = {
        'col_val': 'CMg',
        'gr_cols': ['Escenario', 'Barra', 'Agno', 'Mes', 'Latitud'],
        'gr_fin': ['Barra', 'Agno', 'Mes', 'Latitud'],
        'piv_ind': ['Barra', 'Latitud'],
        'sort_or': ['Latitud', 'Barra'],
        'col_dur': 'Horas'
    }
    data_gen  = {
        'col_val': 'Energia',
        'gr_cols': ['Escenario', 'Central', 'Combustible', 'Tipo', 'Agno',
                    'Mes'],
        'gr_fin': ['Central', 'Combustible', 'Tipo', 'Agno', 'Mes'],
        'piv_ind': ['Central', 'Combustible', 'Tipo'],
        'sort_or': ['Tipo', 'Central'],
        'col_dur': None,
        'pr_comb': 'PreComb',
        'cv_comb': 'CostoVar'
    }
    data_tran = {
        'tr_cols': ['DiaTipo', 'Bloque', 'Escenario', 'Linea', 'fecha', 'uso'],
        'piv_ind': ['DiaTipo', 'Bloque', 'Escenario', 'Linea'],
        'piv_val': 'uso'
    }

    def __init__(self, file_name):
        self.file_name = Path(file_name)
        self.carpeta_F = self.file_name.parent

    def open_file(self):
        if self.file_name.name.endswith('xlsx'):
            return pd.read_excel(self.file_name, sheet_name=None, header=2)
        else:
            return super().open_file()

    def calc_mean(self, df):
        if 'CMg'.lower() in self.file_name.name.lower():
            if 'TOCMg'.lower() not in self.file_name.name.lower():
                raise ValueError(f'File {self.file_name.name} must be MENSUAL')
            data = self.data_cmg

        if 'Gen'.lower() in self.file_name.name.lower():
            if 'TOGen'.lower() not in self.file_name.name.lower():
                raise ValueError(f'File {self.file_name.name} must be MENSUAL')
            data = self.data_gen
            if 'Combustible' not in df.columns:
                df['Combustible'] = ''

        df = self._Modex__calc_med(df, data['col_val'], data['col_dur'],
                                   data['gr_cols'], data['gr_fin'])
        self._Modex__cols_to_date(df)
        df = self._Modex__pivotea(
            df, 'fecha', data['col_val'], data['piv_ind'], data['sort_or'])

        if not data['col_dur']:
            df['Total'] = df.sum(numeric_only=True, axis=1)
            df = df.loc[df['Total'] != 0]

        print(f'Mean Calculation Process Finished: {self.file_name}')
        return df

    def combustible(self, df):
        if 'TOGenTer'.lower() not in self.file_name.name.lower():
            raise ValueError(f'File {self.file_name.name} must be TOGenTer')
        data = self.data_gen
        df = df.groupby(data['gr_fin'],
                        as_index=False).agg({data['pr_comb']: np.mean,
                                             data['cv_comb']: np.mean})
        self._Modex__cols_to_date(df)
        pre = self._Modex__pivotea(
            df, 'fecha', data['pr_comb'], data['piv_ind'], data['sort_or'])
        cv = self._Modex__pivotea(
            df, 'fecha', data['cv_comb'], data['piv_ind'], data['sort_or'])
        print(f'Combustible Process Finished: {self.file_name}')
        return pre, cv

    def transmision(self, df, val_uso):
        if 'TOTran'.lower() not in self.file_name.name.lower():
            raise ValueError(f'File {self.file_name.name} must be TOTran')
        df['uso'] = np.where(df['Potencia'] < 0,
                             df['Potencia'] / df['PMaxB_A'],
                             df['Potencia'] / df['PMaxA_B'])
        self._Modex__cols_to_date(df)
        data = self.data_tran
        df = df[data['tr_cols']]
        df = df.query(f'({val_uso} <= uso) | (uso <= -{val_uso})')
        df = self._Modex__pivotea(
            df, 'fecha', data['piv_val'], data['piv_ind'])
        print(f'Transmission Process Finished: {self.file_name}')
        return df



