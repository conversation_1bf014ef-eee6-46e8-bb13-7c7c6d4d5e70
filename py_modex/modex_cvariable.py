from py_modex.modex import PyModex
import pandas as pd
import numpy as np


class CvarModex(PyModex):
    def __init__(self):
        super().__init__()
        self.hoja   	  = self.sh_CVar
        self.cvar         = self.hoja.range('D3').value
        self.tipo_res     = self.hoja.range('D4').value
        self.fecha        = self.hoja.range('D5').value
        self.grabar       = self.hoja.range('D6').value
        self.hoja.range('B16').expand().value = None
        self.hoja.range('F3:i11').value = None

    def extrae_cvar_imodex(self):
        modex_ar = 'TOGenTer.parquet'
        # reconoce primero si existe parquet, sino sale del programa
        df = self.reconoce_parq(modex_ar, self.hoja)
        cvar, nom_ar = self.deja_listo_para_filtrar(self.cvar)        
        
        self.mensajes('Filtrando', self.hoja)
        nom = '|'.join(cvar)
        df = df.dropna()
        df = df.loc[df['Central'].str.contains(nom)]
        
        if isinstance(self.fecha, float):
            df = df.loc[df['Agno'] == self.fecha]
        elif (self.fecha != None) and ('-' in self.fecha):	
            fi, ff = self.fecha.split('-')[0], self.fecha.split('-')[1]
            try:
                df = df.loc[df['Agno'].between(int(fi), int(ff))]
            except:
                self.mensajes('algo ocurrio (fechas deben ser numeros?)', 
                    self.hoja, sigue=False)
                        
        return df, nom_ar

    def cvar_modex(self):
        try:
            CVar, nom_ar = self.extrae_cvar_imodex()

            # -- Preparando Dataframes
            self.mensajes('Preparando Salida', self.hoja)

            if self.tipo_res=='Tecnología':
                CVar = CVar.groupby(
                    ['Agno', 'Mes', 'Tipo']).agg({'CostoVar': np.mean})
                indice = ['Tipo']
            elif self.tipo_res == 'Sin resumir':
                CVar = CVar.groupby(
                    ['Agno', 'Mes', 'Central', 'Tipo']).agg({'CostoVar': np.mean})
                indice = ['Central', 'Tipo']
            
            CVar = pd.pivot_table(
                        data=CVar, 
                        columns=['Agno', 'Mes'],
                        index=indice,
                        values='CostoVar'
                        )
            CVar = CVar.ffill(axis=1)
                
            self.hoja.range('B16').options(index=True,
                                           expand='table').value = CVar           
            self.mensajes('Término de proceso', self.hoja)

            # -- Exportando archivo csv filtrado
            if self.grabar != 'no':
                self.mensajes('Exportando Archivo', self.hoja)
                self._grabar_df(CVar, nom_ar, formato=self.grabar, index=True)                

        except Exception as _error:
            self.mensajes(f'error desconocido: {_error}', self.hoja, 
                          sigue=False)
