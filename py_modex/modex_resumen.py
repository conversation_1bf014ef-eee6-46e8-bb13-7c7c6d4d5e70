from py_modex.modex import PyModex
import pandas as pd
import time


class Resumenes(PyModex):
	def __init__(self):
		super().__init__()
		self.hoja   	  = self.sh_res_cmg

	def extrae_embalses(self):		
		modex_ar = 'embalses_sal.parquet'
		# reconoce primero si existe parquet, sino sale del programa
		df = self.reconoce_parq(modex_ar, 'modexemb.parquet', self.hoja)
		emb, nom_ar = self.deja_listo_para_filtrar(self.emb)
		
		self.mensajes('Filtrando embalses', self.hoja)
		#self.mensajes(df.columns, self.hoja)
		nom = '|'.join(emb)
		df['may'] = df['EmbNom'].str.upper()
		df = df.loc[df['may'].str.contains(nom)]

		df['EmbNom'] = df['EmbNom'].str.replace(' ', '')
		if self.simulaciones!='Todas':
			sim = self.simulaciones
			if isinstance(sim, str): 
				sim = sim.replace(' ', '')
				if '-' in sim:
					s1 = sim.split('-')[0]
					s2 = sim.split('-')[1]
					try:
						df = df.loc[df['Hidro'].between(int(s1), int(s2))]
						return df, nom_ar
					except:
						self.mensajes(
							'Debe ingresar "Todas", números en campo "Sims", '
       						'o sims separada por "-"', 
							self.hoja, sigue=False)
				else:
					sim = sim.split(',')
					sim = [int(x) for x in sim]

				sim = sim.split(',')
				sim = [int(x) for x in sim]
			elif isinstance(sim, float):
				sim = [int(sim)]
			else:
				self.mensajes('Debe ingresar "Todas" o números en campo "Sims"', 
                              self.hoja, sigue=False)  
			df = df.loc[df['Hidro'].isin(sim)]
		return df, nom_ar

	def res_cmg(self):
		tipo      = self.hoja.range('D4').value
		fecha     = self.hoja.range('D5').value
		escenario = self.hoja.range('D6').value
  
		try:
			# leyendo Dataframe
			#df = self.sh_CMg.range('J300').expand().options(pd.DataFrame).value
			df = self.sh_CMg.range('B15').expand()

			# -- Preparando Dataframes
			
			self.hoja.range('B16').options(
       								index=True,
									expand='table').value = df
						

		except Exception as _error:
			self.mensajes(f'error desconocido: {_error}', 
                 self.hoja, sigue=False)
