from py_modex.modex import PyModex
import pandas as pd


class GenModex(PyModex):
    def __init__(self):
        super().__init__()
        self.hoja         = self.sh_Gen
        self.gen          = self.hoja.range('D3').value
        self.tipo_res     = self.hoja.range('D4').value
        self.tipo_res_2   = self.hoja.range('D5').value
        self.fecha        = self.hoja.range('D6').value
        self.simulaciones = self.hoja.range('D7').value
        self.grabar       = self.hoja.range('D8').value
        self.hoja.range('B16').expand().value = None
        self.hoja.range('B18').expand().value = None
        self.hoja.range('D16').expand().value = None
        self.hoja.range('C17').expand().value = None
        self.hoja.range('F3:i11').value = None

    def extrae_gen_modexcen(self):
        modex_ar = 'modex_gen.parquet'
        # reconoce primero si existe parquet, sino sale del programa
        df = self.reconoce_parq(modex_ar, self.hoja)
        gen, nom_ar = self.deja_listo_para_filtrar(self.gen)
        
        self.mensajes('Filtrando generadores', self.hoja)
        nom = '|'.join(gen)
        df = df.loc[df['Central'].str.contains(nom)]
        if df.count()[0] == 0:
            self.mensajes('Generador no encontrado', self.hoja, sigue=False)
    
        df['Central'] = df['Central'].str.replace(' ', '')
        if self.simulaciones!='Todas':
            sim = self.simulaciones
            df = df.loc[df['Escenario'].isin([sim])]
            if len(df)==0:
                self.mensajes('Debe ingresar "Todas" o nombre exacto en ' \
        					  'campo "Escenario"', 
                              self.hoja, sigue=False)  
		
        res_1 = ['Escenario', 'Agno']
        if self.tipo_res == 'Mensual':
            res_1 = res_1 + ['Mes']
        elif self.tipo_res == 'Bloque':
            res_1 = res_1 + ['Bloque']
        elif self.tipo_res == 'DiaTipo':
            res_1 = res_1 + ['DiaTipo']
        elif self.tipo_res == 'Mensual-Bloque':
            res_1 = res_1 + ['Mes', 'Bloque']
        elif self.tipo_res == 'Mensual-DiaTipo':
            res_1 = res_1 + ['Mes', 'DiaTipo']
            
        if self.tipo_res_2 == 'Central':
            res_1 = res_1 + ['Central', 'Tipo']  
        elif self.tipo_res_2 == 'Tecnologia':
            res_1 = res_1 + ['Tipo']
        elif self.tipo_res_2 == 'ZonaGeo':
            res_1 = res_1 + ['ZonaGeo']
        elif self.tipo_res_2 == 'Tec-ZonaGeo':
            res_1 = res_1 + ['ZonaGeo', 'Tipo']
        elif self.tipo_res_2 == 'Todas':
            res_1 = res_1 + ['Central', 'ZonaGeo', 'Tipo']
        
        if isinstance(self.fecha, float):
            df = df.loc[df['Agno'] == self.fecha]
        elif (self.fecha != None) and ('-' in self.fecha):	
            fi, ff = self.fecha.split('-')[0], self.fecha.split('-')[1]
            try:
                df = df.loc[df['Agno'].between(int(fi), int(ff))]
            except:
                self.mensajes('algo ocurrio (fechas deben ser numeros?)', 
                    self.hoja, sigue=False)
            
        df = df.groupby(res_1, as_index=False)['Energia'].sum()
        return df, nom_ar

    def gen_modex(self):
        try:
            # -- Abriendo Archivos
            Gen, nom_ar = self.extrae_gen_modexcen()

            # -- Preparando Dataframes
            self.mensajes('Preparando Salida', self.hoja)
            
            # -- Calculando Precio Medio
            columnas = ['Agno']
            col_index = ['Escenario']
            if self.tipo_res=='Mensual':
                columnas = columnas + ['Mes']
            elif self.tipo_res == 'Bloque':
                columnas = columnas + ['Bloque']
            elif self.tipo_res == 'DiaTipo':
                columnas = columnas + ['DiaTipo']
            elif self.tipo_res == 'Mensual-Bloque':
                columnas = columnas + ['Mes', 'Bloque']
            elif self.tipo_res == 'Mensual-DiaTipo':
                columnas = columnas + ['Mes', 'DiaTipo']
            
            if self.tipo_res_2 == 'Central':
                col_index = col_index + ['Central', 'Tipo']
            elif self.tipo_res_2 == 'Tecnologia':
                col_index = col_index + ['Tipo']
            elif self.tipo_res_2 == 'ZonaGeo':
                col_index = col_index + ['ZonaGeo']
            elif self.tipo_res_2 == 'Tec-ZonaGeo':
                col_index = col_index + ['ZonaGeo', 'Tipo']
            elif self.tipo_res_2 == 'Todas':
                col_index = col_index + ['Central', 'ZonaGeo', 'Tipo']                
                
            gen = pd.pivot_table(
                        data=Gen, 
                        columns=columnas,
                        index=col_index,
                        values='Energia'
                        )
            self.hoja.range('B16').options(index=True,
                                             expand='table').value = gen           
            self.mensajes('Término de proceso', self.hoja)

            # -- Exportando archivo csv filtrado
            if self.grabar != 'no':
                self.mensajes('Exportando Archivo', self.hoja)
                self._grabar_df(gen, nom_ar, formato=self.grabar, index=True)                

        except Exception as cmg_error:
            self.mensajes(f'error desconocido: {cmg_error}', self.hoja, 
                          sigue=False)

def calcP_med(df_sol, hidro='calendario', banda=False):
    agno, mes = ('Ano_cal', 'Mes_cal') if hidro == 'calendario' \
        else ('Ano_hid', 'Mes_hid')
    cols = ['Hidro', 'CenNom', agno, mes]
    ind = ['CenNom', agno, mes]

    if banda:
        ind.append('Banda_hor')
        cols.append('Banda_hor')

    df_sol['gen_x_hr'] = df_sol['CenEgen'] * df_sol['Duracion']
    df_sol = df_sol.groupby(cols, as_index=False).sum()

    df_sol['CenEgen'] = df_sol['gen_x_hr'] / df_sol['Duracion']
    cols.append('CenEgen')
    df_sol = df_sol.pivot_table(index=ind, columns='Hidro',
                                values='CenEgen')

    return df_sol
