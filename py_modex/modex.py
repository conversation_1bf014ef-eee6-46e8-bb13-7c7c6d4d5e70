import xlwings as xw
import pandas as pd
import numpy as np
from pathlib import Path
import time


class PyModex:
    def __init__(self):
        self.wb         = xw.Book.caller()
        self.sh_ctrl    = self.wb.sheets('Control')
        self.sh_CMg     = self.wb.sheets('CMg')
        self.sh_Gen     = self.wb.sheets('Generacion')
        self.sh_Lin     = self.wb.sheets('Lineas')
        self.sh_CVar    = self.wb.sheets('CVar')
        self.sh_PObr    = self.wb.sheets('PlanObras')
        self.sh_emb     = self.wb.sheets('Embalses')
        self.sh_ret     = self.wb.sheets('Retiros x Barra')
        self.sh_dic_gen = self.wb.sheets('dic_gen')
        self.sh_res_cmg = self.wb.sheets('Resumen CMg')
        self.imodex		= self.sh_ctrl.range('D5').value
        self.path       = Path(self.wb.fullname).parent
        
        self.c_in = Path.joinpath(self.path, self.sh_ctrl.range('D3').value)
        self.c_out = Path.joinpath(self.path, self.sh_ctrl.range('D4').value)
        Path(self.c_in).mkdir(parents=True, exist_ok=True)
        Path(self.c_out).mkdir(parents=True, exist_ok=True)
        self.pos_msg = 3
    
    def _leer_salida(self, ar_pqt, ar, hoja, col='f'):
        try:
            if 'parquet' in ar_pqt:
                df = pd.read_parquet(Path.joinpath(self.c_in, ar_pqt))
            if 'cmg' in ar:
                df = _arregla_df(df, 'cmg')
                self._toParquet(df, ar, 'cmg')
                self._toParquet(df, 'ret.parquet', 'ret')
                df = df[['Bloque', 'Hidro', 'BarNom', 'CMgBar']]
                return df
            elif 'cen' in ar:
                df = _arregla_df(df, 'gen')
                self._toParquet(df, ar, 'cen')
                self._toParquet(df, 'iny.parquet', 'iny')
                df = df[['Bloque', 'Hidro', 'CenNom', 'CenEgen']]
                return df
            elif 'lin' in ar:
                df = _arregla_df(df, 'lin')
                self._toParquet(df, ar, 'lin')
                df = df[['Bloque', 'Hidro', 'LinNom', 'LinUso']]
                return df
            elif 'embalses' in ar:
                df = _arregla_df(df, 'emb')
                self._toParquet(df, ar, 'emb')
                df = df[['Bloque', 'Hidro', 'EmbNom', 'EmbPsom',
                         'EmbVfin', 'EmbVini', 'EmbQgen', 'EmbPsom2']]
                return df
            elif 'imodex' in ar:
                # Se aprovecha de sacar la info de costos variables,
                # # Plan de obras, generadores, etapas, etc, desde la imodex
                xls = pd.ExcelFile(Path.joinpath(self.c_in, ar_pqt))
                df = pd.read_excel(xls, sheet_name='Combustibles', header=2,
                                   usecols='B:E')
                self._toParquet(df, 'imodex_cvar.parquet', 'otro')
                df = pd.read_excel(xls, sheet_name='CenTer', header=2,
                                   usecols='B:Y')
                self._toParquet(df, 'imodex_centrales.parquet', 'otro')
        except Exception as f_error:
            self.mensajes(f'Archivo salida no encontrado: {str(f_error)}', 
                          hoja, col=col)

    def reconoce_parq(self, nombre_parquet, hoja, col='F'):
        if Path.joinpath(self.c_in, nombre_parquet).exists():
            self.mensajes(f'Abriendo {nombre_parquet}', hoja, col=col)
            df = pd.read_parquet(Path.joinpath(self.c_in, nombre_parquet))
        elif nombre_parquet == 'imodex_pobras.parquet':
            self.mensajes(f'Creando: {nombre_parquet}' , hoja, col=col)
            df = pd.read_excel(
                Path.joinpath(self.c_in, self.imodex), 
                sheet_name='CenDisp',
                usecols='C:F',
                header=2)
            df = df.to_parquet(Path.joinpath(self.c_in, nombre_parquet))
            df = pd.read_parquet(Path.joinpath(self.c_in, nombre_parquet))

        elif nombre_parquet == 'tecnologia.parquet':
            self.mensajes(f'Creando: {nombre_parquet}' , hoja, col=col)
            ex = pd.ExcelFile(Path.joinpath(self.c_in, self.imodex))
            df = pd.DataFrame(columns=['Unidad', 'TipoCen', 'PotMax'])
            
            sh_names = ['CenHid', 'CenRen', 'CenAcu', 'CenTer']
            pmax = ['M', 'K', 'H', 'I']
            for shn, pm in zip(sh_names, pmax):
                df_aux = pd.read_excel(ex, 
                                       index_col=None,
                                       sheet_name=shn, 	
                                       header=2, 
                                       usecols='B,C,' + pm)
                if shn == 'CenAcu':
                    df_aux.rename(columns={'PotDes': 'PotMax'}, inplace=True)
                df = pd.concat([df, df_aux], join="inner")
            df = df.to_parquet(Path.joinpath(self.c_in, nombre_parquet))
            df = pd.read_parquet(Path.joinpath(self.c_in, nombre_parquet))

        elif nombre_parquet == 'modex_gen.parquet':
            self.mensajes(f'Creando: {nombre_parquet}' , hoja, col=col)
            df = pd.DataFrame(columns=[
                'DiaTipo', 'Bloque', 'Escenario', 'Central', 
                'ZonaGeo', 'Tipo', 'Agno', 'Mes', 
                'PotMax', 'Energia', 'IngSpot'])
            archivos_gen = [
                'TOGenHid.parquet',
                'TOGenTer.parquet',
                'TOGenRen.parquet',
                'TOGenAcu.parquet'
                ]
            for ar in archivos_gen:
                df_aux = pd.read_parquet(Path.joinpath(self.c_in, ar))
                df = pd.concat([df, df_aux], join="inner")
            df = df.to_parquet(Path.joinpath(self.c_in, nombre_parquet))
            df = pd.read_parquet(Path.joinpath(self.c_in, nombre_parquet))
        else:
            self.mensajes(f'No se encuentra archivo: {nombre_parquet}',
                          hoja, col=col, sigue=False)
        return df

    def deja_listo_para_filtrar(self, seleccion):
        seleccion = str(int(seleccion)) if isinstance(seleccion, float) \
            							else seleccion
        if ',' in seleccion:
            nom_ar = seleccion.replace(', ', '_')
            seleccion = seleccion.replace(' ', '')
            seleccion  = seleccion.upper().split(',')
        else:
            nom_ar = seleccion
            seleccion = [seleccion.upper()]
        return seleccion, nom_ar

    def _toParquet(self, df, nombre_parq, tipo):
        if tipo == 'cmg':
            df = df[['Bloque', 'Hidro', 'BarNom', 'CMgBar']]
        elif tipo == 'ret':
            df = df[['Bloque', 'Hidro', 'BarNom', 'DemBarP', 'DemBarE',
                     'BarRetP', 'BarRetE']]
        elif tipo == 'cen':
            df = df[['Bloque', 'Hidro', 'CenNom', 'CenEgen']]
        elif tipo == 'iny':
            df = df[['Bloque', 'Hidro', 'BarNom', 'CenInyP', 'CenInyE']]
        elif tipo == 'sim_hid':
            df = df[['Bloque', 'Anno', 'Banda_hor', 'Duracion', 'Ano_hid',
                     'Mes_hid', 'Ano_cal', 'Mes_cal', 'Bloque_dia']]
        elif tipo == 'lin':
            df = df[['Bloque', 'Hidro', 'LinNom', 'LinUso']]
        
        df.to_parquet(Path.joinpath(self.c_in, nombre_parq), engine='pyarrow')

    def _leer_parquet(self, my_file):
        df = pd.read_parquet(
            Path.joinpath(self.c_in, my_file), 
            engine='pyarrow'
            )
        return df

    def _grabar_df(self, df, nombre, formato, index=False):
        opciones = {'index': index, 'encoding': 'latin'}
        nom = nombre + '.' + formato
        if formato == 'csv':
            df.to_csv(Path.joinpath(self.c_out, nom), sep=',', **opciones)
        elif formato == 'tsv':
            df.to_csv(Path.joinpath(self.c_out, nom), sep='\t', **opciones)
        elif formato == 'xlsx':
            df.to_excel(Path.joinpath(self.c_out, nom), **opciones)

    def mensajes(self, msg, hoja, sigue=True, col='F', tiempo=0):
        cel = col + str(self.pos_msg)
        mensaje = hoja.range(cel)
        if msg!='' and hoja != self.sh_ctrl:
            mensaje.value = msg
        if tiempo!=0:
            mensaje.offset(-1, 3).value = round(time.time() - tiempo, 3)
            self.pos_msg -= 1
        if sigue:
            self.pos_msg += 1
        else:
            exit()

    def _leer_csv(self, ar_csv, in_out, header=0):
        opt = {'encoding':'latin', 'header': header}
        if in_out == 'cin':
            df = pd.read_csv(self.c_in / ar_csv, **opt)
        elif in_out == 'cout':
            df = pd.read_csv(self.c_out / ar_csv, **opt)
        return df

    def resumen_cmg():
        pass

def _arregla_df(df, tipo):    
    if tipo != 'pre':
        df['Hidro'] = df['Hidro'].str.rstrip()
        df['Hidro'] = df['Hidro'].str.replace("Sim", "")
        simuls = pd.to_numeric(df['Hidro'],
								'coerce').fillna(0).astype(np.int64).max()
        df.loc[df['Hidro'] == 'MEDIA', 'Hidro'] = simuls + 1
        df['Hidro'] = df['Hidro'].astype('int64')
        if tipo == 'cmg':
            df['BarNom'] = df['BarNom'].str.rstrip()
            return df
        elif tipo == 'gen':
            df['BarNom'] = df['BarNom'].str.rstrip()
            df['CenNom'] = df['CenNom'].str.rstrip()
            return df
        elif tipo == 'lin':
            df['LinNom'] = df['LinNom'].str.rstrip()
            df.loc[df['LinFluE'] < 0, 'LinUso'] *= -1
        elif tipo == 'emb':
            df['EmbNom'] = df['EmbNom'].str.rstrip()
        return df
    else:
        simuls = pd.to_numeric(df['Hidro'],
		                       'coerce').fillna(0).astype(np.int64).max()
        df.rename(columns={'Hidro': 'Sim'}, inplace=True)
        return df, simuls

