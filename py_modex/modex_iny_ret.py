from py_modex.modex import PyModex
import pandas as pd
import time


class InyRetModex(PyModex):
    def __init__(self):
        super().__init__()
        self.hoja         = self.sh_ret
        self.barra        = self.hoja.range('D3').value
        self.tipo_res     = self.hoja.range('D4').value
        self.simulaciones = self.hoja.range('D5').value
        self.grabar       = self.hoja.range('D6').value
        self.hoja.range('B16').expand().value = None
        self.hoja.range('F3:i11').value = None

    def extrae_retiros_imodex(self):
        modex_ret = 'ret.parquet'
        # reconoce primero si existe parquet, sino sale del programa
        df = self.reconoce_parq(modex_ret, 'modexbar.parquet', self.hoja)
        self.mensajes('', self.hoja, tiempo=time.time())
        ret, nom_ar = self.deja_listo_para_filtrar(self.barra)
                
        self.mensajes('Filtrando', self.hoja)
        nom = '|'.join(ret)
        df = df.loc[df['BarNom'].str.contains(nom)]
        if self.simulaciones!='Todas':
            sim = self.simulaciones
            if isinstance(sim, str): 
                sim = sim.replace(' ', '')
                if '-' in sim:
                    s1 = sim.split('-')[0]
                    s2 = sim.split('-')[1]
                    try:
                        df = df.loc[df['Hidro'].between(int(s1), int(s2))]
                        return df, nom_ar
                    except:
                        self.mensajes(
                            'Debe ingresar "Todas", números en campo "Sims", '
                            'o sims separada por "-"', 
                            self.hoja, sigue=False)
                else:
                    sim = sim.split(',')
                    sim = [int(x) for x in sim]

                sim = sim.split(',')
                sim = [int(x) for x in sim]
            elif isinstance(sim, float):
                sim = [int(sim)]
            else:
                self.mensajes('Debe ingresar "Todas" o números en campo "Sims"', 
                              self.hoja, sigue=False)  
            df = df.loc[df['Hidro'].isin(sim)]
        return df, nom_ar
    
    def retiros_modex(self):
        try:
            ret, nom_ar = self.extrae_retiros_imodex()

            # -- Preparando Dataframes
            self.mensajes('Preparando Salida', self.hoja)
            ret = pd.pivot_table(
                        data=ret, 
                        columns='Bloque',
                        index=['Hidro', 'BarNom'],
                        values= 'DemBarE'               #'DemBarE', 'BarRetE'
                        )            

            self.hoja.range('B16').options(index=True,
                                           expand='table').value = ret
            self.mensajes('Término de proceso', self.hoja)

            # -- Exportando archivo csv filtrado
            if self.grabar != 'no':
                self.mensajes('Exportando Archivo', self.hoja)
                self._grabar_df(ret, nom_ar, formato=self.grabar, index=True)

        except Exception as _error:
            self.mensajes(f'error desconocido: {_error}', self.hoja, 
                          sigue=False)
