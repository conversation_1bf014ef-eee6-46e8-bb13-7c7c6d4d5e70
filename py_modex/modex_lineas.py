from py_modex.modex import PyModex
import pandas as pd
import numpy as np


class LinModex(PyModex):
    def __init__(self):
        super().__init__()
        self.hoja         = self.sh_Lin
        self.lin          = self.hoja.range('D3').value
        self.tipo_res     = self.hoja.range('D4').value
        self.tipo_res_2   = self.hoja.range('D5').value
        self.fecha        = self.hoja.range('D6').value
        self.simulaciones = self.hoja.range('D7').value
        self.grabar       = self.hoja.range('D8').value
        self.hoja.range('B16').expand().value = None
        self.hoja.range('B18').expand().value = None
        self.hoja.range('D16').expand().value = None
        self.hoja.range('C17').expand().value = None
        self.hoja.range('F3:i11').value = None

    def extrae_lin_modexlin(self):
        modex_ar = 'TOTran.parquet'
        # reconoce primero si existe parquet, sino sale del programa
        df = self.reconoce_parq(modex_ar, self.hoja)
        lin, nom_ar = self.deja_listo_para_filtrar(self.lin)
        
        self.mensajes('Filtrando Lineas', self.hoja)
        nom = '|'.join(lin)
        df['may'] = df['Linea'].str.upper()
        df = df.loc[df['may'].str.contains(nom)]
        df['Linea'] = df['Linea'].str.replace(' ', '')
        
        if self.simulaciones!='Todas':
            sim = self.simulaciones
            df = df.loc[df['Escenario'].isin([sim])]
            if len(df)==0:
                self.mensajes('Debe ingresar "Todas" o nombre exacto en ' \
        					  'campo "Escenario"', 
                              self.hoja, sigue=False)  
                
        if isinstance(self.fecha, float):
            df = df.loc[df['Agno'] == self.fecha]
        elif (self.fecha != None) and ('-' in self.fecha):	
            fi, ff = self.fecha.split('-')[0], self.fecha.split('-')[1]
            try:
                df = df.loc[df['Agno'].between(int(fi), int(ff))]
            except:
                self.mensajes('algo ocurrio (fechas deben ser numeros?)', 
                    self.hoja, sigue=False)
        
        df['Uso'] = np.where(df.Potencia <= 0, 
                             df.Potencia/df.PMaxB_A, 
                             df.Potencia/df.PMaxA_B)
        return df, nom_ar

    def lin_modex(self):
        try:
            # -- Abriendo Archivos
            Lin, nom_ar = self.extrae_lin_modexlin()

            # -- Preparando Dataframes
            self.mensajes('Preparando Salida', self.hoja)
            
            columnas = ['Agno']            
            if self.tipo_res == 'Mensual':
                columnas = columnas + ['Mes']
            elif self.tipo_res == 'Bloque':
                columnas = columnas + ['Bloque']
            
            value = ''
            if self.tipo_res_2 == 'Uso%':
                value = 'Uso'
            elif self.tipo_res_2 == 'Energia':
                value = 'Energia'
            
            lin = pd.pivot_table(
                    data=Lin, 
                    columns=columnas,
                    index=['Escenario', 'Linea'],
                    values=value
                    )
            self.hoja.range('B16').options(index=True,
                                             expand='table').value = lin           
            self.mensajes('Término de proceso', self.hoja)

            # -- Exportando archivo csv filtrado
            if self.grabar != 'no':
                self.mensajes('Exportando Archivo', self.hoja)
                self._grabar_df(lin, nom_ar, formato=self.grabar, index=True)                

        except Exception as cmg_error:
            self.mensajes(f'error desconocido: {cmg_error}', self.hoja, 
                          sigue=False)
