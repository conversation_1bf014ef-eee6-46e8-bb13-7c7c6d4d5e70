from py_modex.modex import PyModex
import pandas as pd

class CmgModex(PyModex):
	def __init__(self):
		super().__init__()
		self.hoja   	  = self.sh_CMg
		self.barra        = self.hoja.range('D3').value
		self.tipo_res     = self.hoja.range('D4').value
		self.fecha        = self.hoja.range('D5').value
		self.simulaciones = self.hoja.range('D6').value
		self.grabar       = self.hoja.range('D7').value
		self.hoja.range('B15').expandtable.value = None
		self.hoja.range('D14').expand().value = None
		self.hoja.range('F3:i11').value = None

	def extrae_barra_modexbar(self):		
		modex_ar = 'TOCMg.parquet'
		# reconoce primero si existe parquet, sino sale del programa
		df = self.reconoce_parq(modex_ar, self.hoja)
		barra, nom_ar = self.deja_listo_para_filtrar(self.barra)
		
		self.mensajes('Filtrando barras', self.hoja)
		nom = '|'.join(barra)
		df['may'] = df['Barra'].str.upper()
		df = df.loc[df['may'].str.contains(nom)]

		df['Barra'] = df['Barra'].str.replace(' ', '')
		if self.simulaciones!='Todas':
			sim = self.simulaciones
			df = df.loc[df['Escenario'].isin([sim])]
			if len(df)==0:
				self.mensajes('Debe ingresar "Todas" o nombre exacto en ' \
        					  'campo "Escenario"', 
                              self.hoja, sigue=False)
		if isinstance(self.fecha, float):
			df = df.loc[df['Agno'] == self.fecha]
		elif (self.fecha != None) and ('-' in self.fecha):	
			fi, ff = self.fecha.split('-')[0], self.fecha.split('-')[1]
			try:
				df = df.loc[df['Agno'].between(int(fi), int(ff))]
			except:
				self.mensajes('algo ocurrio (fechas deben ser numeros?)', 
						self.hoja, sigue=False)

				
		if self.tipo_res == 'Mensual':
			df['cmg_hor'] = df['CMg'] * df['Horas']
			df = df.groupby(['Escenario', 'Barra', 'Agno', 'Mes'],
                   as_index=False)['CMg', 'cmg_hor', 'Horas'].sum()
			df['CMg'] = df['cmg_hor'] / df['Horas']
			df.drop(columns=['cmg_hor'], inplace=True)
		elif self.tipo_res == 'Bloque':
			#df_prob = pd.read_excel(str(self.c_in) + '\\' + 'TOProb.xlsx')
			#df = df.merge(df_prob, how='left', on='Escenario')
			df['cmg_hor'] = df['CMg'] * df['Horas']
   
			#df['cmg_hor_pro'] = df['cmg_hor'] * df['Probabilidad']
			#df['hor_pro'] = df['Horas'] * df['Probabilidad']

			df = df.groupby(['Escenario', 'Barra', 'Bloque', 'Agno'],
                   as_index=False)['CMg', 'cmg_hor', 'Horas'].sum()
			# Calcular cmg promedio
			df['CMg'] = df['cmg_hor'] / df['Horas']

			# Eliminar columnas auxiliares
			df.drop(columns=['cmg_hor'], inplace=True)

		return df, nom_ar

	def cmg_modex(self):
		try:
			# -- Abriendo Archivos
			CMg, nom_ar = self.extrae_barra_modexbar()

			# -- Preparando Dataframes
			self.mensajes('Preparando archivos para cálculo', self.hoja)

			columnas = ['Agno', 'Mes', 'DiaTipo', 'Bloque']
			if self.tipo_res == 'Mensual':
				columnas = ['Agno', 'Mes']
			elif self.tipo_res == 'Bloque':
				columnas = ['Agno', 'Bloque']
			else:
				pass

			cmg = pd.pivot_table(
						data=CMg, 
						columns=columnas,
						index=['Escenario', 'Barra'],
						values='CMg'
						)
			self.hoja.range('B14').options(index=True,
                                  		   expand='table').value = cmg
			
			self.mensajes('Término de Proceso', self.hoja)
   
			# -- Exportando archivo csv filtrado
			if self.grabar != 'no':
				self.mensajes('Exportando archivo', self.hoja)
				self._grabar_df(cmg, nom_ar, formato=self.grabar, index=True)				

		except Exception as _error:
			self.mensajes(f'error desconocido: {_error}', 
                 self.hoja, sigue=False)


