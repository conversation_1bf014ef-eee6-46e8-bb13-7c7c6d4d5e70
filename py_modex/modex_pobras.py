from py_modex.modex import PyModex
import pandas as pd
import time


class PoModex(PyModex):
    def __init__(self):
        super().__init__()
        self.hoja         = self.sh_PObr
        self.po           = self.hoja.range('D3').value
        self.tipo_res     = self.hoja.range('D4').value
        self.fecha        = self.hoja.range('D5').value
        self.grabar       = self.hoja.range('D6').value
        self.hoja.range('B16:BA18').expand().value = None
        self.hoja.range('F3:i11').value = None

    def extrae_pobras_imodex(self):
        modex_ar = 'imodex_pobras.parquet'
        # reconoce primero si existe parquet, sino sale del programa
        df = self.reconoce_parq(modex_ar, self.hoja)
        self.mensajes('', self.hoja, tiempo=time.time())
        pobr, nom_ar = self.deja_listo_para_filtrar(self.po)
                
        self.mensajes('Filtrando', self.hoja)
        nom = '|'.join(pobr)
        
        df = df.dropna()
        df = df.loc[df['Unidad'].str.contains(nom)]
        
        if isinstance(self.fecha, float):
            df = df.loc[df['Agno'] == self.fecha]
        elif (self.fecha != None) and ('-' in self.fecha):	
            fi, ff = self.fecha.split('-')[0], self.fecha.split('-')[1]
            try:
                df = df.loc[df['Agno'].between(int(fi), int(ff))]
            except:
                self.mensajes('algo ocurrio (fechas deben ser numeros?)', 
                    self.hoja, sigue=False)        
        return df, nom_ar
    
    def pobras_modex(self):
        try:
            Pobr, nom_ar = self.extrae_pobras_imodex()

            # -- Preparando Dataframes
            self.mensajes('Preparando Salida', self.hoja)
            df_tec = self.reconoce_parq('tecnologia.parquet', self.hoja)
            Pobr = Pobr.merge(df_tec, on='Unidad')
            
            columnas = ['Agno', 'Mes', 'TipoCen', 'Tipo']
            if self.tipo_res=='Tecnología':
                indice = ['TipoCen', 'Tipo']
            elif self.tipo_res=='Sin resumir':
                columnas = ['Unidad'] + columnas
                indice = ['Unidad', 'TipoCen', 'Tipo']
            
            Pobr = Pobr.groupby(columnas).agg({'PotMax': sum})    
            Pobr = pd.pivot_table(
                        data=Pobr, 
                        columns=['Agno', 'Mes'],
                        index=indice,
                        values='PotMax'
                        )
            
            Pobr.sort_values('Tipo', inplace=True)
            #Pobr = Pobr.ffill(axis=1)
            self.hoja.range('B16').options(index=True,
                                           expand='table').value = Pobr           
            self.mensajes('Término de proceso', self.hoja)

            # -- Exportando archivo csv filtrado
            if self.grabar != 'no':
                self.mensajes('Exportando Archivo', self.hoja)
                self._grabar_df(Pobr, nom_ar, formato=self.grabar, index=True)                

        except Exception as _error:
            self.mensajes(f'error desconocido: {_error}', self.hoja, 
                          sigue=False)
