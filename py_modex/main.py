from py_modex import modex_cmg, modex_gen, modex_lineas, modex_cvariable
from py_modex import modex_iny_ret, carga_archivos, modex_embalses, modex_pobras
from py_modex import modex_resumen
import time

def main(tipo):
	t_ini = time.time()
	if tipo == 'cmg':
		cmg = modex_cmg.CmgModex()
		try:
			cmg.cmg_modex()
		except Exception as cmg_error:
			cmg.mensajes(f'error desconocido: {cmg_error}', 
                cmg.hoja, sigue=False)

		menj = str(round(time.time() - t_ini, 2))
		cmg.mensajes(f'tiempo: {menj}', cmg.hoja)
	elif tipo == 'gen':
		gen = modex_gen.GenModex()
		try:
			gen.gen_modex()
		except Exception as gen_error:
			gen.mensajes(f'error desconocido: {gen_error}', 
                gen.hoja, sigue=False)

		menj = str(round(time.time() - t_ini, 2))
		gen.mensajes(f'tiempo: {menj}', gen.hoja)
	elif tipo == 'lin':
		lin = modex_lineas.LinModex()
		try:
			lin.lin_modex()
		except Exception as lin_error:
			lin.mensajes(f'error desconocido: {lin_error}', 
                lin.hoja, sigue=False)

		menj = str(round(time.time() - t_ini, 2))
		lin.mensajes(f'tiempo: {menj}', lin.hoja)
	elif tipo == 'cvar':
		cvar = modex_cvariable.CvarModex()
		try:
			cvar.cvar_modex()
		except Exception as _error:
			cvar.mensajes(f'error desconocido: {_error}', 
                 cvar.hoja, sigue=False)

		menj = str(round(time.time() - t_ini, 2))
		cvar.mensajes(f'tiempo: {menj}', cvar.hoja)
	elif tipo == 'pobras':
		pob = modex_pobras.PoModex()
		
		try:
			pob.pobras_modex()
		except Exception as _error:
			pob.mensajes(f'error desconocido: {_error}', pob.hoja, 
                sigue=False)

		menj = str(round(time.time() - t_ini, 2))
		pob.mensajes(f'tiempo: {menj}', pob.hoja)
	elif tipo == 'iny_ret':
		iny_ret = modex_iny_ret.InyRetModex()
		
		try:
			iny_ret.retiros_modex()
		except Exception as _error:
			iny_ret.mensajes(f'error desconocido: {_error}', iny_ret.hoja, 
                sigue=False)

		menj = str(round(time.time() - t_ini, 2))
		iny_ret.mensajes(f'tiempo: {menj}', iny_ret.hoja)
	elif tipo == 'carga_etapa':
		etapas = carga_archivos.CargaArchivos()
		
		try:
			etapas.carga_etapas()
		except Exception as _error:
			etapas.mensajes(f'error desconocido: {_error}', etapas.hoja_m, 
                sigue=False, col='k')
	elif tipo == 'embalses':
		emb = modex_embalses.EmbalsesPLP()
		
		try:
			emb.emb_modex()
		except Exception as _error:
			emb.mensajes(f'error desconocido: {_error}', emb.hoja, 
                sigue=False)
	elif tipo == 'resumen_cmg':
		resumen = modex_resumen.Resumenes()
		
		try:
			resumen.res_cmg()
		except Exception as _error:
			resumen.mensajes(f'error desconocido: {_error}', resumen.hoja, 
                sigue=False)

