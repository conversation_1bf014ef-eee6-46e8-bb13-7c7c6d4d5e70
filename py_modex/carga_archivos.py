from py_modex.modex import PyModex
import pandas as pd
import numpy as np


class CargaArchivos(PyModex):
    def __init__(self):
        super().__init__()
        self.hoja_m  = self.sh_ctrl
        self.hoja1   = self.sh_etapas
        self.pos_msg = 10
    
    def carga_etapas(self):
        try:
            #etapas = self.reconoce_parq('etapas.parquet', self.imodex, self.hoja_m)
            etapas = self._leer_parquet('etapas.parquet')
            etapas.columns = etapas.columns.str.strip()
            etapas = etapas[['Nombre', 'Tipo', 'Duracion', 'Mes']]
            
            etapas.rename(
                columns={'Nombre': 'Bloque', 'Tipo': 'Etapa'}, 
                inplace=True)
            
            etapas['Bloque'] =  pd.to_numeric(
                etapas['Bloque'].str[-4:], 'coerce').fillna(0).astype(np.int64)
            etapas['Etapa'] =  pd.to_numeric(
                etapas['Etapa'].str[-4:], 'coerce').fillna(0).astype(np.int64)
            
            self.hoja1.range('A1').options(
                                    index=False,
                                    expand='table').value = etapas
            
            df = self.reconoce_parq('imodex_etapas.parquet', 
                                    self.imodex, self.hoja_m, 'k')
            self.hoja1.range('G1').options(
                                    index=False,
                                    expand='table').value = df
        except Exception as _error:
            self.mensajes(f'error desconocido: {_error}', 
                    self.hoja_m, sigue=False, col='k')
