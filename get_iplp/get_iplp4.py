import pandas as pd
import calendar
import random
import time
import datetime
import numpy as np

class Proc_IPLP:
    def __init__(self):
        """
        Recibe
        i) archivo como input
        ii) dict con true/false para procesar
        """

        self.config_file = pd.ExcelFile("config.xlsx")

        self.config_gral = pd.read_excel(
            self.config_file, sheet_name="General", usecols="B:C", index_col=0
        ).to_dict()["Action"]

        self.file_name = self.config_gral["File_name_IPLP"]
        aux_ = self.file_name.replace(".xlsm", "")
        print("Proc datos de " + aux_ + "\n")
        self.file = pd.ExcelFile(self.file_name)
        self.d_in = self.config_gral
        self.rnd_dig = 6
        self.df_centrales = self.get_datos_centrales()
        g_fechas = self.get_fechas()
        self.df_fechas = g_fechas["frame"]
        self.list_dates = g_fechas["list"]
        self.ultimo_agno = g_fechas["ultimo_agno"]
        self.sheet_gas = "CentralGas"
        self.sheet_descr = "Descripciones"
        self.sheet_elim = "CentralElim"
        self.keep_redundant_data = self.config_gral["Keep_redundant_data"]
        self.group_by_date = self.config_gral["Group_by_date"]
        self.agno_mante = self.config_gral["Year_mantenimiento"]
        self.dict_descripciones = self.dict_descripcion_map()
        self.l_drop_centrales = self.lista_centrales_eliminadas()

    def main(self):
        dict_output = {}

        if self.d_in["proc_mante"] == "si":
            dict_output.update(self.get_mantenimientos())
        if self.d_in["proc_limit"] == "si":
            dict_output.update(self.get_limitaciones())
        if self.d_in["proc_combu"] == "si":
            dict_output.update(self.get_combustibles())
        if self.d_in["proc_cini"] == "si":
            dict_output.update(self.get_c_iniciales())

        if len(dict_output) > 0:
            self.print_results(dict_output)

        #return dict_output

    def print_results(self, dict_):
        now = datetime.datetime.now()
        dt_string = now.strftime("%Y_%m_%d_%H_%M_%S")
        aux = self.file_name.replace(".xlsm", "")
        #name = "Proc_" + aux + "_" + dt_string + ".xlsx"
        name = "Proc_" + aux + ".xlsx"
        with pd.ExcelWriter(name) as writer:
            for k, v in dict_.items():
                sht_name = k
                v.to_excel(
                    writer, sheet_name=sht_name, header=True, index=False
                )

    def get_fechas(self, sht="Etapas"):
        """
        Procesa hoja Etapas
        Output: dict con 2 keys
        i) {frame}: DF con fechas diarias con formato
        yyyy-dd-mm, desde primer hasta ultimo dia
        considerado en PLP
        ii) {list}: lista con fechas de i) con
        el mismo formato
        """
        df_eta = pd.read_excel(
            self.file,
            sheet_name=sht,
            skiprows=3,
            names=["inicial", "final"],
            usecols="B,D",
            parse_dates=[0, 1],
            engine="openpyxl",
        )

        ini_dat = df_eta["inicial"].min()
        fin_dat = df_eta["final"].max()

        agno_fin = fin_dat.year

        lis_dat = pd.date_range(start=ini_dat, end=fin_dat, freq="D")
        df_dat = lis_dat.to_frame(index=False)
        df_dat.columns = ["fecha"]
        df_dat["key"] = 1
        d_output = {"frame": df_dat, "list": lis_dat, "ultimo_agno": agno_fin}
        return d_output

    def get_datos_centrales(
        self, sht="Centrales", col_names=["central", "minima", "maxima"]
    ):
        """
        Procesa hoja Centrales, y permite obtener todas
        las centrales consideradas en PLP en formato
        central, potmin, potmax
        Retorna DF con datos de centrales
        en formato: central, minima, maxima
        """
        df_centrales = pd.read_excel(
            self.file,
            sheet_name=sht,
            skiprows=4,
            names=col_names,
            usecols="B,AA,AB",
            engine="openpyxl",
        )
        df_centrales["minima"] = df_centrales["minima"].round(self.rnd_dig)
        df_centrales["maxima"] = df_centrales["maxima"].round(self.rnd_dig)
        return df_centrales

    def get_mantenimientos(self, tipo="mante"):
        """
        Procesa hoja Mantenimiento Mayor(4), para
        obtener la tabla de input para Modex y Modex Hor.
        Para Modex, se determina: PotMedia min y max
        si difieren de valores nominales.
        Para ModexHor, se determina: PotMedia min y max,
        dias de mant proporcionales a 1 semana, y dia de inicio
        de mantenimiento. Este dia es aleatorio entre lunes
        y domingo - dias de mantenimiento. En caso que los dias equivalentes
        sean menor a 0, se escribe 0 dias, y NA para el dia de inicio

        Centrales a Gas se reportan como 1 sola, y no por tipo de gas
        que utiliza
        """

        # Se obtiene df_exd que corresponde a DF con expanded dates
        # de los dias de mantenimiento, y su potencia respectiva
        v_aux = self.get_expanded_data(tipo)
        df_exd = v_aux["df_expanded_dates"]
        # Filtramos df con TODAS las centrales en IPLP, para
        # tener centrales con mantenimiento solamente
        # df filtrado corresponde a df_sel
        df_sel = v_aux["df_centrales_selected"]
        df_filter = v_aux["df_filter"]
        cat_order = v_aux['l_order_descr']
        # create empty df with selected plants and all dates
        df_dates = self.df_fechas
        df_res = self.proc_all_days(df_sel, df_dates, df_exd, df_filter, tipo)
        # Obtener proporcion de dias con mant para ModexHor
        # Asignar dia aleatorio inicio mant

        df_dias_mant = self.count_days_mant(df_exd)
        df_res = self.join_mantenimientos(df_res, df_dias_mant)
        df_res = self.join_descripcion(df_res, df_exd)
        # Agregar informacion de ModexHor
        df_pot_mdxh = self.get_potencia_mdx_hor(df_exd)
        df_res = self.join_potencia_mdx_hor(df_res, df_pot_mdxh)
        df_res = self.centrales_tipo_gas(df_res)
        df_res = self.update_nombres_modex(df_res, tipo)
        # Por ahora no considerar en mante, dado que hay
        # pot modex y modex hor
        if False:
            if self.d_in["Group_by_date"] == "si":
                df_res = self.group_by_date_ranges(df_res, tipo)

        # self.create_excel(df_res, tipo)

        df_res_repl = self.replicate_years_mante(df_res)
        d_out = {"Mante": df_res, "Mante_R": df_res_repl}

        return d_out

    def get_limitaciones(self, tipo="limit"):
        # Se obtiene df_exd que corresponde a DF con expanded dates
        # de los dias de mantenimiento, y su potencia respectiva
        v_aux = self.get_expanded_data(tipo)
        df_exd = v_aux["df_expanded_dates"]
        # Filtramos df con TODAS las centrales en IPLP, para
        # tener centrales con mantenimiento solamente
        # df filtrado corresponde a df_sel
        df_sel = v_aux["df_centrales_selected"]
        df_filter = v_aux["df_filter"]
        cat_order = v_aux['l_order_descr']
        # create empty df with selected plants and all dates
        df_dates = self.df_fechas

        df_res = self.proc_all_days(df_sel, df_dates, df_exd, df_filter, tipo)
        df_res = self.join_descripcion(df_res, df_exd)
        df_res = self.centrales_tipo_gas(df_res)
        df_res = self.update_nombres_modex(df_res, tipo)
        if self.d_in["Group_by_date"] == "si":
            df_res = self.group_by_date_ranges(df_res, tipo)
        # self.create_excel(df_res, tipo)
        df_res = self.sort_by_categorical(df_res, cat_order)
        d_out = {"Limit": df_res}
        return d_out

    def get_c_iniciales(self, tipo="cini"):
        # Se obtiene df_exd que corresponde a DF con expanded dates
        # de los dias de mantenimiento, y su potencia respectiva
        v_aux = self.get_expanded_data(tipo)
        df_exd = v_aux["df_expanded_dates"]
        # Filtramos df con TODAS las centrales en IPLP, para
        # tener centrales con mantenimiento solamente
        # df filtrado corresponde a df_sel
        df_sel = v_aux["df_centrales_selected"]
        df_filter = v_aux["df_filter"]
        cat_order = v_aux['l_order_descr']
        # create empty df with selected plants and all dates
        df_dates = self.df_fechas
        df_res = self.proc_all_days(df_sel, df_dates, df_exd, df_filter, tipo)
        d = self.obtener_descripcion(df_exd)
        df_res = self.join_descripcion(df_res, df_exd)
        df_res = self.centrales_tipo_gas(df_res)
        df_res = self.update_nombres_modex(df_res, tipo)
        if self.d_in["Group_by_date"] == "si":
            df_res = self.group_by_date_ranges(df_res, tipo)
        # self.create_excel(df_res, tipo)
        df_res = self.sort_by_categorical(df_res, cat_order)

        d_out = {"C.Ini": df_res}
        return d_out

    def get_combustibles(self, tipo="combu"):
        # Se obtiene df_exd que corresponde a DF con expanded dates
        # de los dias de mantenimiento, y su potencia respectiva
        v_aux = self.get_expanded_data(tipo)
        df_exd = v_aux["df_expanded_dates"]
        # Filtramos df con TODAS las centrales en IPLP, para
        # tener centrales con mantenimiento solamente
        # df filtrado corresponde a df_sel
        df_sel = v_aux["df_centrales_selected"]
        df_filter = v_aux["df_filter"]
        # create empty df with selected plants and all dates
        df_dates = self.df_fechas
        df_res = self.proc_all_days(df_sel, df_dates, df_exd, df_filter, tipo)
        d = self.obtener_descripcion(df_exd)
        df_res = self.join_descripcion(df_res, df_exd)
        # df_res = self.centrales_tipo_gas(df_res)
        df_res = self.update_nombres_modex(df_res, tipo)
        if self.d_in["Group_by_date"] == "si":
            df_res = self.group_by_date_ranges(df_res, tipo)
        df_res["Unidad"] = "MWavg"
        df_res = self.rename_combu_df(df_res)
        # self.create_excel(df_res, tipo)
        d_out = {"Combu": df_res}
        return d_out

    def get_expanded_data(self, opc_nam):
        """
        Procesa hoja opc_name de IPLP. Expande rango de fechas
        a fecha diaria, y luego cruza centrales con la informacion
        nominal de las centrales en hoja Centrales IPLP
        """
        # Se obtiene df_exd que corresponde a DF con expanded dates
        # de los dias de mantenimiento, y su potencia respectiva

        # Por otro lado, Filtramos df_centrales que contiene
        # TODAS las centrales en IPLP, para
        # tener centrales con mantenimiento solamente
        # df filtrado corresponde a df_sel

        # sheet names
        if opc_nam == "limit":
            sht_name = "Limitaciones(3)"
        elif opc_nam == "mante":
            sht_name = "Mantenimiento Mayor(4)"
        elif opc_nam == "combu":
            sht_name = "Disp. Combustibles(2)"
        elif opc_nam == "cini":
            sht_name = "C.Iniciales(1)"

        opc_col = [
            "descripcion",
            "central",
            "inicial",
            "final",
            "minima",
            "maxima",
        ]

        # read availability/fuels data
        df_ava = pd.read_excel(
            self.file,
            sheet_name=sht_name,
            skiprows=4,
            names=opc_col,
            usecols="A:F",
            parse_dates=[2, 3],
            engine="openpyxl",
        )

        # fill missing descriptions forward
        if opc_nam == "cini":
            df_ava = self.pre_process_cini(df_ava)
        elif opc_nam == 'limit':
            df_ava = self.pre_process_limit(df_ava)
        else:
            df_ava.fillna(method="ffill", inplace=True)

        lista_order = self.order_of_descripciones(df_ava)

        # Drop centrales eliminadas
        df_ava = self.drop_centrales_elim(df_ava)

        # Filter not needed fuels data
        if opc_nam == "combu":
            # Natural gas only
            df_ava = df_ava[
                df_ava["descripcion"].isin(["NT GNL", "Disp. GNL"])
            ]
            # filter fuels data for diesel plants
            df_ava = df_ava[~df_ava["central"].str.contains("_DIE")]

        print("procesar datos " + opc_nam)
        # expand df replacing interval records with data for all days
        df_exp = pd.concat(
            [
                pd.DataFrame(
                    {
                        "descripcion": row.descripcion,
                        "central": row.central,
                        "fecha": pd.date_range(row.inicial, row.final),
                        "minima": row.minima,
                        "maxima": row.maxima,
                    },
                    columns=[
                        "descripcion",
                        "central",
                        "fecha",
                        "minima",
                        "maxima",
                    ],
                )
                for i, row in df_ava.iterrows()
            ],
            ignore_index=True,
        )
        # discard data out of dates range
        df_exd = df_exp.loc[df_exp["fecha"].isin(self.list_dates)].copy()
        # discard duplicates, leaving last occurrence
        df_exd.drop_duplicates(
            subset=["central", "fecha"],
            keep="last",
            inplace=True,
            ignore_index=True,
        )
        df_exd.sort_values(
            by=["central", "fecha"], inplace=True, ignore_index=True
        )
        df_exd.set_index(keys=["central", "fecha"], drop=True, inplace=True)

        # list of plants with maintenance (fuels) data
        lis_pla = df_ava.central.unique()
        lis_pla.sort()
        # select plants with data
        df_pla = self.df_centrales
        df_sel = df_pla.loc[df_pla["central"].isin(lis_pla)].copy()
        df_sel.sort_values(by=["central"], inplace=True, ignore_index=True)
        df_sel["key"] = 1

        # no fuel availability if not defined
        # for maintenance data, base is minimum and maximum
        if opc_nam == "combu":
            # minima & maxima = 0
            df_sel["minima"] = 0.0
            df_sel["maxima"] = 0.0

        # procesar df_exd para obtener central/agno/mes
        # cuando se tenga mante/limit solamente

        df_filter = self.obtener_dates(df_exd)

        # retorna diccionario con 2 keys.
        d_output = {
            "df_expanded_dates": df_exd,
            "df_centrales_selected": df_sel,
            "df_filter": df_filter,
            "l_order_descr":lista_order,
        }

        return d_output

    def proc_all_days(self, _dfsel, _dfdates, _dfexd, _df_filter, tipo):
        # df all corresponde a df con todas las fechas del periodo IPLP
        # donde se considera central, fecha, min, max, y min y max
        # vienen de situacion nominal (sin mantenimiento)
        # a continuacion, se hace update de potencias min y max
        # segun la info de los mantenimientos

        df_sel = _dfsel.copy()
        df_dates = _dfdates.copy()
        df_exd = _dfexd.copy()
        df_all = pd.merge(df_sel, df_dates, how="outer", on="key")
        df_sel.drop(columns=["key"], inplace=True)
        df_all.drop(columns=["key"], inplace=True)
        df_all.set_index(keys=["central", "fecha"], drop=True, inplace=True)

        print("actualizar " + tipo)
        # update df_all values with those from data changes
        df_all.update(df_exd, join="left", overwrite=True, errors="ignore")
        df_all.reset_index(inplace=True)

        print("resultado " + tipo + "\n")

        # average by year & month
        df_all["agno"] = pd.DatetimeIndex(df_all["fecha"]).year
        df_all["mes"] = pd.DatetimeIndex(df_all["fecha"]).month

        df_mon = df_all.groupby(["central", "agno", "mes"], as_index=False)[
            ["minima", "maxima"]
        ].mean()
        # round minima, maxima
        df_mon["minima"] = df_mon["minima"].round(self.rnd_dig)
        df_mon["maxima"] = df_mon["maxima"].round(self.rnd_dig)

        # aplicar funcion filter_dates. se filtra df_mon, pues
        # este df tiene datos por todo el periodo IPLP (si
        # no tiene mante/limit para algunos meses, los rellena
        # con potencia nominales)
        df_mon = self.filter_dates(df_mon, _df_filter)
        # discard records equal to base data (keep changes only)
        if tipo != "mante":
            if self.keep_redundant_data == "si":
                df_res = df_mon.copy()
                df_res.sort_values(
                    by=["central", "agno", "mes"],
                    inplace=True,
                    ignore_index=True,
                )
            else:
                df_res = pd.merge(
                    df_mon,
                    df_sel,
                    how="outer",
                    indicator=True,
                    on=["central", "minima", "maxima"],
                ).loc[lambda x: x["_merge"] == "left_only"]
                df_res.drop(columns=["_merge"], inplace=True)
                df_res.sort_values(
                    by=["central", "agno", "mes"],
                    inplace=True,
                    ignore_index=True,
                )
        else:
            df_res = pd.merge(
                df_mon,
                df_sel,
                how="outer",
                indicator=True,
                on=["central", "minima", "maxima"],
            ).loc[lambda x: x["_merge"] == "left_only"]
            df_res.drop(columns=["_merge"], inplace=True)

            df_res.sort_values(
                by=["central", "agno", "mes"], inplace=True, ignore_index=True
            )

        # discard 0 values for fuels
        if tipo == "combu":
            df_res = df_res.loc[df_res["maxima"] != 0]

        # change column names and duplicate
        df_res.rename(columns={"agno": "agno_i", "mes": "mes_i"}, inplace=True)
        df_res["agno_f"] = df_res["agno_i"]
        df_res["mes_f"] = df_res["mes_i"]
        df_res = df_res.reindex(
            columns=[
                "central",
                "agno_i",
                "mes_i",
                "agno_f",
                "mes_f",
                "minima",
                "maxima",
            ]
        )

        return df_res

    def join_mantenimientos(self, _df_in, df_dias_in):
        _df = _df_in.copy()
        df_dias = df_dias_in.copy()

        _df = _df.merge(
            df_dias,
            how="left",
            left_on=["central", "agno_i", "mes_i"],
            right_on=["central", "agno", "mes"],
        )
        _df.drop(columns=["agno", "mes"], inplace=True)

        return _df

    def join_descripcion(self, _df_res, _df_exd):
        df_res = _df_res.copy()
        df_descr = _df_exd.copy()

        df_descr = self.obtener_descripcion(df_descr)

        df_res = df_res.merge(
            df_descr,
            how="left",
            left_on=["central", "agno_i", "mes_i"],
            right_on=["central", "agno", "mes"],
        )
        df_res.drop(columns=["agno", "mes"], inplace=True)

        return df_res

    def count_days_mant(self, _df_exd):
        """
        Obtiene i) proporcion de dias/mes en mantencion
        ii) dia de inicio aleatorio
        Output: DF formato:
        central, agno, mes, dias_mant, dia_i
        """
        df_dias = _df_exd.copy()
        df_dias.reset_index(inplace=True)
        df_dias["agno"] = pd.DatetimeIndex(df_dias["fecha"]).year
        df_dias["mes"] = pd.DatetimeIndex(df_dias["fecha"]).month
        df_dias = (
            df_dias.groupby(["central", "agno", "mes"])["maxima"]
            .count()
            .to_frame()
        )
        df_dias.reset_index(inplace=True)
        df_dias.rename(columns={"maxima": "num_dias_mant"}, inplace=True)
        df_dias["num_dias_mes"] = df_dias.apply(
            lambda x: calendar.monthrange(x["agno"], x["mes"])[1], axis=1
        )
        df_dias["dias_mant"] = round(
            7 * df_dias["num_dias_mant"] / df_dias["num_dias_mes"], 0
        )

        random.seed(2454563)
        df_dias["dia_max_inicio"] = 7 - df_dias["dias_mant"]
        df_dias["dia_inicio"] = df_dias.apply(
            lambda x: random.randint(0, x["dia_max_inicio"]), axis=1
        )
        d_map = {
            0: "1-LU",
            1: "2-MA",
            2: "3-MI",
            3: "4-JU",
            4: "5-VI",
            5: "6-SA",
            6: "7-DO",
            7: "NA",
        }
        df_dias["dia_i"] = df_dias.apply(
            lambda x: d_map[x["dia_inicio"]], axis=1
        )
        df_dias.drop(
            columns=[
                "num_dias_mant",
                "num_dias_mes",
                "dia_max_inicio",
                "dia_inicio",
            ],
            inplace=True,
        )
        # Asigna NA a dias con 0 dias mant
        df_dias.loc[df_dias["dias_mant"] == 0, "dia_i"] = "NA"

        return df_dias

    def centrales_tipo_gas(self, _df_):
        # Actualizar central -> Tipo_Gas
        _df = _df_.copy()
        df_nombres = pd.read_excel(
            self.config_file,
            sheet_name=self.sheet_gas,
            skiprows=2,
            usecols="B:C",
        )
        dict_map = df_nombres.set_index("Tipo_Gas").to_dict()["Central"]

        _df["central"] = _df["central"].replace(dict_map)

        _df.drop_duplicates(
            subset=[
                "central",
                "agno_i",
                "mes_i",
                "agno_f",
                "mes_f",
                "descripcion",
            ],
            inplace=True,
            ignore_index=True,
        )

        return _df

    def rename_combu_df(self, _combu_df):
        df_res = _combu_df.copy()
        combu_ = {
            "Central": "Tipo_Gas",
            "PotMin": "MinMes",
            "PotMax": "MaxMes",
        }
        df_res.rename(columns=combu_, inplace=True)
        return df_res

    def update_nombres_modex(self, _df, tipo):
        df = _df.copy()
        mant_ = {
            "central": "Central",
            "agno_i": "AgnoIni",
            "mes_i": "MesIni",
            "agno_f": "AgnoFin",
            "mes_f": "MesFin",
            "dia_i": "MdxHo_DiaIni",
            "dias_mant": "MdxHo_NumDias",
            "minima": "Mdx_PotMin",
            "maxima": "Mdx_PotMax",
            "descripcion": "Descripcion",
        }
        other_ = {
            "central": "Central",
            "agno_i": "AgnoIni",
            "mes_i": "MesIni",
            "agno_f": "AgnoFin",
            "mes_f": "MesFin",
            "minima": "PotMin",
            "maxima": "PotMax",
            "descripcion": "Descripcion",
        }

        d_rename = {
            "mante": mant_,
            "limit": other_,
            "combu": other_,
            "cini": other_,
        }

        df.rename(columns=d_rename[tipo], inplace=True)

        # Establecer orden de columnas
        mant_col = [
            "Descripcion",
            "Central",
            "AgnoIni",
            "MesIni",
            "AgnoFin",
            "MesFin",
            "Mdx_PotMin",
            "Mdx_PotMax",
            "MdxHo_DiaIni",
            "MdxHo_NumDias",
            "MdxHo_PotMin",
            "MdxHo_PotMax",
        ]
        other_col = [
            "Descripcion",
            "Central",
            "AgnoIni",
            "MesIni",
            "AgnoFin",
            "MesFin",
            "PotMin",
            "PotMax",
        ]

        cols = {
            "mante": mant_col,
            "limit": other_col,
            "combu": other_col,
            "cini": other_col,
        }

        df = df[cols[tipo]]

        # ordenar valores
        by_cols = ["Descripcion", "Central", "AgnoIni", "MesIni"]
        df.sort_values(
            by=by_cols,
            inplace=True,
            ignore_index=True,
        )

        if False:
            df_nombres = pd.read_excel(
                self.config_file, sheet_name=self.sheet_descr, usecols="C:D"
            )
            dict_map = df_nombres.set_index("Descripcion_IPLP").to_dict()[
                "Descripcion_Modex"
            ]

        # renombrar descripcion
        df["Descripcion"] = df["Descripcion"].replace(self.dict_descripciones)

        return df

    def dict_descripcion_map(self):
        df_nombres = pd.read_excel(
            self.config_file, sheet_name=self.sheet_descr, usecols="C:D"
        )
        dict_map = df_nombres.set_index("Descripcion_IPLP").to_dict()[
            "Descripcion_Modex"
        ]
        return dict_map

    def obtener_descripcion(self, _df_exd):
        """
        Obtener un DF en formato central, agno, mes, descripcion
        y luego cruzarlo con DF
        """
        df = _df_exd.copy()
        df.reset_index(inplace=True)
        df["agno"] = pd.DatetimeIndex(df["fecha"]).year
        df["mes"] = pd.DatetimeIndex(df["fecha"]).month
        df.drop(columns=["fecha", "minima", "maxima"], inplace=True)
        df.drop_duplicates(
            subset=["central", "agno", "mes"],
            keep="last",
            inplace=True,
            ignore_index=True,
        )
        return df

    def obtener_dates(self, _df_exd):
        """
        Esta funcion es igual a obtener_descripcion,
        pero dropea la descripcion. Se obtiene DF
        central/agno/mes para los meses efectivamente
        considerados en IPLP. Este df se cruza con el
        df df_mon (que abarca todo el periodo de corrida
        plp, y no lo que exclusivamente indica el mante/limitacion)
        """
        df = _df_exd.copy()
        df.reset_index(inplace=True)
        df["agno"] = pd.DatetimeIndex(df["fecha"]).year
        df["mes"] = pd.DatetimeIndex(df["fecha"]).month
        df.drop(
            columns=["fecha", "minima", "maxima", "descripcion"], inplace=True
        )
        df.drop_duplicates(
            subset=["central", "agno", "mes"],
            keep="last",
            inplace=True,
            ignore_index=True,
        )
        return df

    def filter_dates(self, _df_mon, _df_filter):
        """
        Esta funcion elimina de df_mon, todos los meses
        que tiene potencia nominal, dejando exclusivamente
        los meses con mante/limit considerados en IPLP.
        """
        df_mon = _df_mon.copy()
        df_filter = _df_filter.copy()
        df_join = df_mon.merge(
            df_filter, how="inner", on=["central", "agno", "mes"]
        )

        return df_join

    def get_potencia_mdx_hor(self, _df_exd):
        """
        Obtiene potencia min/max promedio para los dias
        que tiene mantenimiento.
        Lógica es: Obtener df central, agno,mes,descripcion,minima,maxima
        y luego cruzarla con df all
        """
        df = _df_exd.copy()
        df.reset_index(inplace=True)
        df["agno"] = pd.DatetimeIndex(df["fecha"]).year
        df["mes"] = pd.DatetimeIndex(df["fecha"]).month
        df.drop(columns=["fecha"], inplace=True)
        df_mensual = df.groupby(
            ["central", "agno", "mes", "descripcion"], as_index=False
        )[["minima", "maxima"]].mean()
        df_mensual.rename(
            columns={
                "central": "centralhor",
                "descripcion": "descripcionhor",
                "agno": "agnohor",
                "mes": "meshor",
                "minima": "MdxHo_PotMin",
                "maxima": "MdxHo_PotMax",
            },
            inplace=True,
        )
        return df_mensual

    def join_potencia_mdx_hor(self, _df_res, _df_pot):
        """
        Hacer join entre
        """
        df_res = _df_res.copy()
        df_pot = _df_pot.copy()
        f = df_res.merge(
            df_pot,
            how="left",
            left_on=["central", "agno_i", "mes_i", "descripcion"],
            right_on=["centralhor", "agnohor", "meshor", "descripcionhor"],
        )
        f.drop(
            columns=["descripcionhor", "centralhor", "agnohor", "meshor"],
            inplace=True,
        )

        return f

    def create_excel(self, df, tipo):
        now = datetime.datetime.now()
        dt_string = now.strftime("%Y_%m_%d_%H_%M_%S")
        aux = self.file_name.replace(".xlsm", "")
        name = aux + "_" + tipo + "_" + dt_string + ".xlsx"
        df.to_excel(name, sheet_name=tipo, header=True, index=False)

    def transform_dates_and_drop(self, _df):
        """
        Transforma Agno/Mes Ini/Fin a FechaIni/Fin YYYY-MM-DD.
        DD = 01 para MesIni
        DD = 28 para MesFin
        Dropea Agno/Mes Ini/Fin
        """
        df = _df.copy()
        # Trasnformar type a int
        cols_ = ["AgnoIni", "MesIni", "AgnoFin", "MesFin"]
        df[cols_] = df[cols_].astype(int)

        # Pasar mes a formato MM
        df["MesIni"] = df["MesIni"].map("{:02}".format)
        # create day inicio
        # se crea columna FechaIni formato yyyy-mm-01
        df["FechaIni"] = (
            df["AgnoIni"].astype(str)
            + "-"
            + df["MesIni"].astype(str)
            + "-"
            + "01"
        )
        # se pasa col a tipo datetime para luego seleccionar min
        df["FechaIni"] = pd.to_datetime(df["FechaIni"], format="%Y-%m-%d")

        # se hace equivalente para fecha fin
        # Pasar mes a formato MM
        df["MesFin"] = df["MesFin"].map("{:02}".format)
        # create day inicio
        # se crea columna FechaIni formato yyyy-mm-01
        df["FechaFin"] = (
            df["AgnoFin"].astype(str)
            + "-"
            + df["MesFin"].astype(str)
            + "-"
            + "28"
        )
        # se pasa col a tipo datetime para luego seleccionar min
        df["FechaFin"] = pd.to_datetime(df["FechaFin"], format="%Y-%m-%d")

        # dropear columnas extras
        cols_drop = ["AgnoIni", "MesIni", "AgnoFin", "MesFin"]
        df.drop(columns=cols_drop, inplace=True)

        return df

    def get_grupos_consecutivos(self, _df):
        """
        Se compara fila con anterior, cuando hay cambio, se suma 1, si no,
        se suma 0, y se reporta la suma acumulada (sumcum), por tanto
        cuando hay un grupo igual (descripcion, central, pot min y max)
        tienen el mismo numero en columna group.
        """
        df = _df.copy()
        cols = ["Descripcion", "Central", "PotMin", "PotMax"]
        df["Group"] = df[cols].ne(df[cols].shift()).any(axis=1).cumsum()
        return df

    def min_max_fechas(self, _df):
        """
        Obtiene fecha de inicio minima y fecha de termino maxima
        para cada grupo. luego esta fecha, se debe volver a pasar
        a formato modex AgnoIni/MesIni/AgnoFin/MesFin
        """
        df = _df.copy()
        cols = [
            "Group",
            "Descripcion",
            "Central",
            "PotMin",
            "PotMax",
        ]
        df = (
            df.groupby(cols)
            .agg(
                {"FechaIni": "min", "FechaFin": "max"},
                as_index=False,
                sort=False,
            )
            .reset_index()
        )
        return df

    def dates_to_modex_format(self, _df):
        df = _df.copy()

        df["AgnoIni"] = pd.DatetimeIndex(df["FechaIni"]).year
        df["MesIni"] = pd.DatetimeIndex(df["FechaIni"]).month

        df["AgnoFin"] = pd.DatetimeIndex(df["FechaFin"]).year
        df["MesFin"] = pd.DatetimeIndex(df["FechaFin"]).month

        c_drop = ["Group", "FechaIni", "FechaFin"]
        df.drop(columns=c_drop, inplace=True)
        return df

    def ordenar_df(self, _df, tipo):
        df = _df.copy()
        # Establecer orden de columnas
        mant_col = [
            "Descripcion",
            "Central",
            "AgnoIni",
            "MesIni",
            "AgnoFin",
            "MesFin",
            "Mdx_PotMin",
            "Mdx_PotMax",
            "MdxHo_DiaIni",
            "MdxHo_NumDias",
            "MdxHo_PotMin",
            "MdxHo_PotMax",
        ]
        other_col = [
            "Descripcion",
            "Central",
            "AgnoIni",
            "MesIni",
            "AgnoFin",
            "MesFin",
            "PotMin",
            "PotMax",
        ]

        cols = {
            "mante": mant_col,
            "limit": other_col,
            "combu": other_col,
            "cini": other_col,
        }

        df = df[cols[tipo]]
        return df

    def group_by_date_ranges(self, _df_res, tipo):
        df_res = _df_res.copy()
        df_res = self.transform_dates_and_drop(df_res)
        df_res = self.get_grupos_consecutivos(df_res)
        df_res = self.min_max_fechas(df_res)
        df_res = self.dates_to_modex_format(df_res)
        df_res = self.ordenar_df(df_res, tipo)
        return df_res

    def replicate_years_mante(self, df_res_mante):
        df = df_res_mante.copy()
        if False:
            # dropear MM Tipicos con AgnoIni 2025
            index_drop = df[
                (df["Descripcion"] == "MM Tipicos") & (df["AgnoIni"] >= 2025)
            ].index
            df_ = df.drop(index_drop)
            df_.reset_index(drop=True, inplace=True)
        # Tomar MM Tipicos de ultimos 12 meses para tenerlo como template
        # Logica de seleccion ultimos 12 meses:
        # Filtrar >= ultimo agno - 1, y luego, dropear
        # ultimo agno en los meses <= 3

        # i) filtrar por ultimo agno - 1
        penultimo_agno = self.ultimo_agno - 1
        idx_select = df[
            (df["Descripcion"] == "MM Tipicos")
            & (df["AgnoIni"] >= penultimo_agno)
        ].index
        df_template = df.loc[idx_select].reset_index(drop=True)
        # ii) filtrar por meses <= 3 en ultimo agno -
        idx_select_2 = df_template[
            (df_template["AgnoIni"] == penultimo_agno)
            & (df_template["MesIni"] <= 3)
        ].index
        df_template = df_template.drop(index=idx_select_2).reset_index(
            drop=True
        )
        l = []
        for i in range(1, self.agno_mante - self.ultimo_agno + 1):
            df_aux = df_template.copy()
            df_aux["Descripcion"] = "MM Tipicos Agregados"
            df_aux["AgnoIni"] = df_aux["AgnoIni"] + i
            df_aux["AgnoFin"] = df_aux["AgnoFin"] + i
            l.append(df_aux)
        df_replicated = pd.concat(l)
        df_out = pd.concat([df, df_replicated])

        return df_out

    def order_of_descripciones(self,df_):
        """
        Recibe dataframe leido desde IPLP sin procesar
        Obtiene lista con descripciones segun orden
        presente en IPLP
        """
        df = df_.copy()
        df1 = df[['descripcion']].drop_duplicates()
        df1.reset_index(inplace=True, drop=True)
        df1['descripcion'] = df1['descripcion'].replace(self.dict_descripciones)
        l_output = list(df1['descripcion'])
        return l_output

    def sort_by_categorical(self, _df, categories_):
        df = _df.copy()
        # col descripcion en categorical segun lista categories
        df['Descripcion'] = pd.Categorical(df['Descripcion'],
                                           categories = categories_,
                                           ordered = True)
        # return df con descripcion + resto
        cols_by = ['Descripcion', 'Central', 'AgnoIni','MesIni']
        df.sort_values(by=cols_by, inplace=True, ignore_index=True)
        return df

    def lista_centrales_eliminadas(self):
        df_nombres = pd.read_excel(
            self.config_file, sheet_name=self.sheet_elim, usecols="B",
            skiprows=2)
        l_output = list(df_nombres['Central'])
        return l_output

    def drop_centrales_elim(self,df_ava_):
        df = df_ava_.copy()
        indexCentral = df[df['central'].isin(self.l_drop_centrales)].index
        df.drop(indexCentral, inplace=True)
        df.reset_index(drop=True)
        return df

    def pre_process_limit(self,_df_ava):
        """
        Pre procesa hoja Limites a la fuerza bruta
        1) renombra registros en columna descripcion
        con NA, para ser llenados con ffill
        Retorna df_ava procesado
        """
        df = _df_ava.copy()
        # 1) renombrar registros
        d_descr = {"Lim U3 + Cota":np.NaN,
                   "FAX (27-05-2015)":np.NaN,
                   "FAX - GC 109-2014 (27-08-2014)":np.NaN,}
        df['descripcion'] =df['descripcion'].replace(d_descr)
        # ffill segun modificaciones 1
        df.fillna(method="ffill", inplace=True)
        return df

    def pre_process_cini(self,_df_ava):
        """
        Pre procesa hoja CIniciales a la fuerza bruta
        1) renombra registros en columna descripcion
        con palabra Inicializacion, para luego,
        dropear todos los registros Inicializacion
        2) Mapea desde columna Central a descripcion
        segun corresponda
        Retorna df_ava procesado
        """
        df = _df_ava.copy()
        df_laja = self.laja_combustibles()
        df = pd.concat([df, df_laja], ignore_index=True)
        # 1) renombrar registros
        d_descr = {"Derechos de aprovechamiento ": "Derechos de aprovechamiento mensuales",
                    "mensuales":np.NaN,
                    "Restricciones de": "Inicializacion",
                    "GAS CNE Y GNL": "Inicializacion",
                    "Inicialización GNL": "Inicializacion",
                    "Inicializacion ": "Inicializacion",
                    "Inicialización ": "Inicializacion",
                    "Generación Eólicas": "Inicializacion",
                    "Inicialización Pasadas":"Inicializacion",
                    "Generación Solares":"Inicializacion" }
        df['descripcion'] =df['descripcion'].replace(d_descr)

        # 2) mapear desde centrales a descripcion
        map_d = {
            'MACHICURA': "Limitaciones Machicura",
            "B_LaMina": "Generacion forzada Los Condores hacia La Mina",
            "SAN_CLEMENTE": "Perfil Series San Clemente",
            "CHIBURGO":"Perfil Series Chiburgo",
            "ROBLERIA": "Perfil Series Robleria",
            'LAJA_I':"Limitaciones Laja I en Disponibilidad combustibles"}
        df['descripcion'] = df['central'].map(map_d).fillna(df['descripcion'])

        # ffill segun modificaciones 1 y 2
        df.fillna(method="ffill", inplace=True)

        # dropear registros con Inicializacion
        idx_drop = df[df['descripcion'] == 'Inicializacion'].index
        df.drop(index=idx_drop, inplace=True)
        df.reset_index(inplace=True, drop=True)
        return df

    def laja_combustibles(self):
        """
        Extrae informacion de Laja_I desde
        hoja combustibles, para ser procesada
        en hoja C.Iniciales
        """
        # sheet names
        opc_nam = 'combu'
        if opc_nam == "limit":
            sht_name = "Limitaciones(3)"
        elif opc_nam == "mante":
            sht_name = "Mantenimiento Mayor(4)"
        elif opc_nam == "combu":
            sht_name = "Disp. Combustibles(2)"
        elif opc_nam == "cini":
            sht_name = "C.Iniciales(1)"

        opc_col = [
            "descripcion",
            "central",
            "inicial",
            "final",
            "minima",
            "maxima",
        ]

        # read availability/fuels data
        df = pd.read_excel(
            self.file,
            sheet_name=sht_name,
            skiprows=4,
            names=opc_col,
            usecols="A:F",
            parse_dates=[2, 3],
            engine="openpyxl",
        )

        df = df[df['central'] == 'LAJA_I']
        return df

# if __name__ == "__main__":
    # start_time = time.time()

Proc_IPLP().main()

# print("--- %s seconds ---" % round((time.time() - start_time), 2))
