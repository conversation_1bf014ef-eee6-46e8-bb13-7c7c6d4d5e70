import polars as pl
from pathlib import Path
import xlwings as xw


#%%
def demanda():
    wb  = xw.Book.caller()
    sh = wb.sheets('Macros')
    sh_r = wb.sheets('DemR')
    sh_l = wb.sheets('DemL')
    sh_ld = wb.sheets('DemLD')

    # -------------------------------------------------------------- Archivos
    folder_plp = Path(sh['C4'].value)
    plp = folder_plp / sh['C5'].value
    # -------------------------------------------------------------- Demanda
    hojas = ['Demanda-R', 'Demanda-L', 'Demanda-LD']


#%%
if __name__ == '__main__':
    #%%
    folder_plp = Path(r'C:\Users\<USER>\OneDrive - Grupo CGE\2025\09_Modex\InputsCEN')
    plp = folder_plp / 'IPLP20251001.xlsm'
    #%%
    # Leer la fila 5 (categorías principales - merged cells)
    categories_df = pl.read_excel(str(plp), sheet_name='Demanda-R',
        has_header=False, read_options={"skip_rows": 4, "n_rows": 1}, )
    categories_row = categories_df.row(0)

    headers_df = pl.read_excel(str(plp), sheet_name='Demanda-R',
        has_header=False, read_options={"skip_rows": 5, "n_rows": 1}, )
    subcategories_row = headers_df.row(0)

    # Crear nombres de columnas combinados
    combined_headers = []

    for i, subcategory in enumerate(subcategories_row):
        # Las primeras 2 columnas se mantienen sin modificar (índice)
        if i < 2:
            combined_headers.append(
                str(subcategory) if subcategory else f"Column_{i}")
            continue

        data_column_index = i - 2
        month_index = data_column_index // 4  # Cada 4 columnas cambia el mes

        if month_index < len(categories_row):
            month = categories_row[month_index]
            combined_name = f"{month}_{subcategory}"
        else:
            combined_name = str(subcategory)

        combined_headers.append(combined_name)

    # Leer los datos desde la fila 7
    df = pl.read_excel(str(plp), sheet_name='Demanda-R', has_header=False,
        read_options={"skip_rows": 6}, )

    # Asignar los nombres combinados
    df.columns = combined_headers
    df = df.with_columns(
        pl.when(pl.col("RESIDENCIAL") == "").then(None).otherwise(
            pl.col("RESIDENCIAL")).fill_null(strategy="forward").alias(
            "RESIDENCIAL"))
    #%%
    combined_headers
    #%%
    df
