import polars as pl
from pathlib import Path
import xlwings as xw


#%%
def demanda():
    wb  = xw.Book.caller()
    sh = wb.sheets('Macros')
    sh_r = wb.sheets('DemR')
    sh_l = wb.sheets('DemL')
    sh_ld = wb.sheets('DemLD')

    # -------------------------------------------------------------- Archivos
    folder_plp = Path(sh['C4'].value)
    plp = folder_plp / sh['C5'].value
    # -------------------------------------------------------------- Demanda
    hojas = ['Demanda-R', 'Demanda-L', 'Demanda-LD']


#%%
if __name__ == '__main__':
    #%%
    folder_plp = Path(r'C:\Users\<USER>\OneDrive - Grupo CGE\2025\09_Modex\InputsCEN')
    plp = folder_plp / 'IPLP20251001.xlsm'
    #%%
    # Leer la fila 5 (categorías principales - merged cells)
    categories_df = pl.read_excel(str(plp), sheet_name='Demanda-R',
        has_header=False, read_options={"skip_rows": 4, "n_rows": 1}, )
    cat_row = categories_df.row(0)

    # Leer la fila 6 (subcategorías/headers)
    headers_df = pl.read_excel(str(plp), sheet_name='Demanda-R',
        has_header=False, read_options={"skip_rows": 5, "n_rows": 1}, )
    subcat_row = headers_df.row(0)

    # Crear nombres de columnas combinados
    combined_headers = []
    current_category = None

    for i, (category, subcategory) in enumerate(zip(cat_row, subcat_row)):
        if category is not None and str(category).strip() != "":
            current_category = str(category).strip()

        if i == 0:
            combined_name = str(subcategory)

        elif current_category and subcategory:
            combined_name = f"{current_category}_{subcategory}"
        elif subcategory:
            combined_name = str(subcategory)
        else:
            combined_name = f"Column_{i}"

        combined_headers.append(combined_name)

    # Leer los datos desde la fila 7
    df = pl.read_excel(str(plp), sheet_name='Demanda-R', has_header=False,
        read_options={"skip_rows": 6}, )

    df.columns[2:] = combined_headers
    #%%
    combined_headers
    #%%
    df.columns
