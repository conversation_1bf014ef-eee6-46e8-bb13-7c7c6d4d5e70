import polars as pl
from pathlib import Path
import xlwings as xw


#%%
def demanda():
    wb  = xw.Book.caller()
    sh = wb.sheets('Macros')
    sh_r = wb.sheets('DemR')
    sh_l = wb.sheets('DemL')
    sh_ld = wb.sheets('DemLD')

    # -------------------------------------------------------------- Archivos
    folder_plp = Path(sh['C4'].value)
    plp = folder_plp / sh['C5'].value
    # -------------------------------------------------------------- Demanda
    hojas = ['Demanda-R', 'Demanda-L', 'Demanda-LD']


#%%
if __name__ == '__main__':
    #%%
    folder_plp = Path(r'C:\Users\<USER>\OneDrive - Grupo CGE\2025\09_Modex\InputsCEN')
    plp = folder_plp / 'IPLP20251001.xlsm'
    #%%
    df = pl.read_excel(
        str(plp),
        sheet_name='Demanda-R',
        has_header=True,
        read_options={"skip_rows": 4},
        )
    #%%
    df
