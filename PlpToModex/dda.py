import polars as pl
from pathlib import Path
import xlwings as xw


#%%
def lee_dda_plp(plp, hoja):
    # Leer la fila 5 (categorías principales - merged cells)
    categories_df = pl.read_excel(str(plp), sheet_name=hoja, has_header=False,
                                  read_options={"skip_rows": 4, "n_rows": 1}, )
    categories_row = categories_df.row(0)

    # Leer la fila 6 (subcategorías/headers)
    headers_df = pl.read_excel(str(plp), sheet_name=hoja, has_header=False,
                               read_options={"skip_rows": 5, "n_rows": 1}, )
    subcategories_row = headers_df.row(0)

    # Crear nombres de columnas combinados
    combined_headers = []

    for i, subcategory in enumerate(subcategories_row):
        # Las primeras 2 columnas se mantienen sin modificar (índice)
        if i < 2:
            combined_headers.append(
                str(subcategory) if subcategory else f"Column_{i}")
            continue

        # Para las columnas de datos (desde la 3 en adelante)
        data_column_index = i - 2
        month_index = data_column_index // 4

        if month_index < len(categories_row):
            month = categories_row[month_index]
            # Manejar casos donde month es None o vacío
            if month is None or str(month).strip() == "" or str(
                    month).lower() == "none":
                month = f"Month_{month_index + 1}"  # Fallback name
            combined_name = f"{month}_{subcategory}"
        else:
            combined_name = str(subcategory) if subcategory else f"Column_{i}"

        combined_headers.append(combined_name)

    # Leer los datos SIN schema_overrides primero
    df = pl.read_excel(str(plp), sheet_name=hoja, has_header=False,
                       read_options={"skip_rows": 6}, )

    # Asignar los nombres combinados
    df.columns = combined_headers

    df = df.with_columns(pl.when(
        pl.col(combined_headers[0]) == "").then(None).otherwise(
        pl.col(combined_headers[0])
    ).fill_null(strategy="forward").alias(combined_headers[0]))
    return df.to_pandas()

def demanda():
    wb  = xw.Book.caller()
    sh = wb.sheets('Macros')
    sh_r = wb.sheets('DemR')
    sh_l = wb.sheets('DemL')
    sh_ld = wb.sheets('DemLD')

    # -------------------------------------------------------------- Archivos
    folder_plp = Path(sh['C4'].value)
    plp = folder_plp / sh['C5'].value
    # -------------------------------------------------------------- Demanda
    hojas = ['Demanda-R', 'Demanda-L', 'Demanda-LD']
    hojas = ['Demanda-R']

    for hoja in hojas:
        df = lee_dda_plp(plp, hoja)
        if hoja == 'Demanda-R':
            sh_r.range('A1').options(expand='table').value = df
        elif hoja == 'Demanda-L':
            sh_l.range('A1').options(expand='table').value = df
        elif hoja == 'Demanda-LD':
            sh_ld.range('A1').options(expand='table').value = df


#%%
if __name__ == '__main__':
    #%%
    folder_plp = Path(r'C:\Users\<USER>\OneDrive - Grupo CGE\2025\09_Modex\InputsCEN')
    plp = folder_plp / 'IPLP20251001.xlsm'
    #%%
    def lee_dda_plp(plp, hoja):
        # Leer la fila 5 (categorías principales - merged cells)
        categories_df = pl.read_excel(str(plp), sheet_name=hoja,
            has_header=False, read_options={"skip_rows": 4, "n_rows": 1}, )
        categories_row = categories_df.row(0)

        # Leer la fila 6 (subcategorías/headers)
        headers_df = pl.read_excel(str(plp), sheet_name=hoja, has_header=False,
            read_options={"skip_rows": 5, "n_rows": 1}, )
        subcategories_row = headers_df.row(0)

        # Crear nombres de columnas combinados
        combined_headers = []

        for i, subcategory in enumerate(subcategories_row):
            # Las primeras 2 columnas se mantienen sin modificar (índice)
            if i < 2:
                combined_headers.append(
                    str(subcategory) if subcategory else f"Column_{i}")
                continue

            # Para las columnas de datos (desde la 3 en adelante)
            data_column_index = i - 2
            month_index = data_column_index // 4

            if month_index < len(categories_row):
                month = categories_row[month_index]
                # Manejar casos donde month es None o vacío
                if month is None or str(month).strip() == "" or str(
                        month).lower() == "none":
                    month = f"Month_{month_index + 1}"  # Fallback name
                combined_name = f"{month}_{subcategory}"
            else:
                combined_name = str(
                    subcategory) if subcategory else f"Column_{i}"

            combined_headers.append(combined_name)

        # Leer los datos SIN schema_overrides primero
        df = pl.read_excel(str(plp), sheet_name=hoja, has_header=False,
            read_options={"skip_rows": 6}, )

        # Asignar los nombres combinados
        df.columns = combined_headers

        return df.to_pandas()
    #%%
    df = lee_dda_plp(plp, 'Demanda-L')
    #%%
    df
