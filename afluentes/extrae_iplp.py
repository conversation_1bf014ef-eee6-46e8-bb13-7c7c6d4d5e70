import pandas as pd
from pathlib import Path
import xlwings as xw
import numpy as np

wb = xw.Book.caller()

def get_ah1():
    sh = wb.sheets['Caudales_Ah1']
    clean_ranges('Caudales_Ah1')
    archivo_iplp = sh.range('C2').value
    df = pd.read_excel(archivo_iplp, sheet_name='Caudales_Ah1',
                       header=4, usecols='A:AX', converters={'Date': str})
    df['AÑO'] = np.where(df['AÑO'] >= 60, 1900 + df['AÑO'], 2000 + df['AÑO'])
    df.columns = ['_' + str(x) for x in df.columns]
    sh['A5'].options(expand='table', index=False, header=True).value = df


def calc_prom():
    sh = wb.sheets['Modex_mes']
    df_c = wb.sheets['Caudales_Ah1'].range('A5').expand().value
    df_aux = wb.sheets['Caudales_Ah1'].range('BD5').expand().value

    df = df_c.multiply(df_aux.T, axis=1)
    sh.range('r5').options(
        expand='table', index=False, header=True).value = df


def clean_ranges(sheet_name):
    sheet = wb.sheets[sheet_name]
    if 'Caudales' in sheet_name:
        last_row = sheet['A6'].end('down').row
        sheet.range('A5', 'AX' + str(last_row)).value = None
        sheet.range('D1').value = None
    if sheet_name == 'CMg':
        sheet.range('B7').expand().color = None
        sheet.range('L1').value = None
        sheet.range('L1').color = None