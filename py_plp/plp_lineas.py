from py_plp.plp import PyPlp
import pandas as pd
import time


class LinPlp(PyPlp):
    def __init__(self):
        super().__init__()
        self.hoja         = self.sh_Lin
        self.lin          = self.hoja.range('D3').value
        self.tipo_res     = self.hoja.range('D4').value
        self.simulaciones = self.hoja.range('D5').value
        self.grabar       = self.hoja.range('D6').value
        self.hoja.range('B16').expand().value = None
        self.hoja.range('F3:i11').value = None

    def extrae_lin_plplin(self):
        plp_ar = 'lin.parquet'
        # reconoce primero si existe parquet, sino sale del programa
        df = self.reconoce_parq(plp_ar, 'plplin.parquet', self.hoja)
        lin, nom_ar = self.deja_listo_para_filtrar(self.lin)
        
        self.mensajes('Filtrando Lineas', self.hoja)
        nom = '|'.join(lin)
        df['may'] = df['LinNom'].str.upper()
        df = df.loc[df['may'].str.contains(nom)]
        df['LinNom'] = df['LinNom'].str.replace(' ', '')
        if self.simulaciones!='Todas':
            sim = self.simulaciones
            if isinstance(sim, str): 
                sim = sim.replace(' ', '')
                if '-' in sim:
                    s1 = sim.split('-')[0]
                    s2 = sim.split('-')[1]
                    try:
                        df = df.loc[df['Hidro'].between(int(s1), int(s2))]
                        return df, nom_ar
                    except:
                        self.mensajes(
                            'Debe ingresar "Todas", números en campo "Sims", '
                            'o sims separada por "-"', 
                            self.hoja, sigue=False)
                else:
                    sim = sim.split(',')
                    sim = [int(x) for x in sim]
                
                sim = sim.split(',')
                sim = [int(x) for x in sim]
            elif isinstance(sim, float):
                sim = [int(sim)]
            else:
                self.mensajes('Debe ingresar "Todas" o números en campo "Sims"', 
                              self.hoja, sigue=False)  
            df = df.loc[df['Hidro'].isin(sim)]
        return df, nom_ar

    def lin_plp(self):
        try:
            # -- Abriendo Archivos
            t1 = time.time()
            Lin, nom_ar = self.extrae_lin_plplin()

            # -- Preparando Dataframes
            self.mensajes('Preparando Salida', self.hoja)
            t1 = time.time()
            lin = pd.pivot_table(
                        data=Lin, 
                        columns='Bloque',
                        index=['Hidro', 'LinNom'],
                        values='LinUso'
                        )
            self.hoja.range('B16').options(index=True,
                                             expand='table').value = lin           
            self.mensajes('Término de proceso', self.hoja)

            # -- Exportando archivo csv filtrado
            if self.grabar != 'no':
                self.mensajes('Exportando Archivo', self.hoja)
                self._grabar_df(lin, nom_ar, formato=self.grabar, index=True)                

        except Exception as cmg_error:
            self.mensajes(f'error desconocido: {cmg_error}', self.hoja, 
                          sigue=False)
