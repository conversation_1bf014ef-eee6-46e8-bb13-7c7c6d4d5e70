from py_plp.plp import PyPlp
import pandas as pd
import numpy as np
import time


class CvarPlp(PyPlp):
    def __init__(self):
        super().__init__()
        self.hoja   	  = self.sh_CVar
        self.cvar         = self.hoja.range('D3').value
        self.tipo_res     = self.hoja.range('D4').value
        self.simulaciones = self.hoja.range('D5').value
        self.grabar       = self.hoja.range('D6').value
        self.hoja.range('B16').expand().value = None
        self.hoja.range('F3:i11').value = None

    def extrae_cvar_iplp(self):
        plp_ar = 'iplp_cvar.parquet'
        # reconoce primero si existe parquet, sino sale del programa
        df = self.reconoce_parq(plp_ar, self.iplp, self.hoja)
        cvar, nom_ar = self.deja_listo_para_filtrar(self.cvar)        
        
        self.mensajes('Filtrando', self.hoja)
        nom = '|'.join(cvar)
        df = df.dropna()
        df = df.loc[df['NAME'].str.contains(nom)]
        return df, nom_ar

    def cvar_plp(self):
        try:
            CVar, nom_ar = self.extrae_cvar_iplp()

            # -- Preparando Dataframes
            self.mensajes('Preparando Salida', self.hoja)
            t1 = time.time()

            col_index = False
            if self.tipo_res=='Tecnología':
                df_dict = self.sh_dic_gen.range('A1').options(
                    pd.DataFrame,
                    expand='table').value
                CVar = CVar.merge(df_dict, left_on='NAME',right_on='CenNom')
                CVar = CVar.groupby(
                    ['NAME', 'DATE1', 'Sub_Tipo']).agg({'CV': np.mean})
                col_index = True
                CVar = pd.pivot_table(
                        data=CVar, 
                        columns='DATE1',
                        index=['NAME', 'Sub_Tipo'],
                        values='CV'
                        )
                CVar = CVar.ffill(axis=1)
            self.hoja.range('B16').options(index=col_index,
                                              expand='table').value = CVar           
            self.mensajes('Término de proceso', self.hoja)

            # -- Exportando archivo csv filtrado
            if self.grabar != 'no':
                self.mensajes('Exportando Archivo', self.hoja)
                self._grabar_df(CVar, nom_ar, formato=self.grabar, index=True)                

        except Exception as _error:
            self.mensajes(f'error desconocido: {_error}', self.hoja, 
                          sigue=False)
