from py_plp.plp import PyPlp
import pandas as pd
import time


class PoPlp(PyPlp):
    def __init__(self):
        super().__init__()
        self.hoja         = self.sh_PObr
        self.po           = self.hoja.range('D3').value
        self.tipo_res     = self.hoja.range('D4').value
        self.simulaciones = self.hoja.range('D5').value
        self.grabar       = self.hoja.range('D6').value
        self.hoja.range('B16').expand().value = None
        self.hoja.range('F3:i11').value = None

    def extrae_pobras_iplp(self):
        plp_ar = 'iplp_pobras.parquet'
        # reconoce primero si existe parquet, sino sale del programa
        df = self.reconoce_parq(plp_ar, self.iplp, self.hoja)
        self.mensajes('', self.hoja, tiempo=time.time())
        pobr, nom_ar = self.deja_listo_para_filtrar(self.po)
                
        self.mensajes('Filtrando', self.hoja)
        nom = '|'.join(pobr)
        df = df.dropna()
        df['MÍNIMA'] += 1
        df = df.loc[df['CENTRAL'].str.contains(nom)]
        return df, nom_ar
    
    def pobras_plp(self):
        try:
            Pobr, nom_ar = self.extrae_pobras_iplp()

            # -- Preparando Dataframes
            self.mensajes('Preparando Salida', self.hoja)            
            col_index = False
            if self.tipo_res=='Tecnología':
                df_dict = self.sh_dic_gen.range('A1').options(
                    pd.DataFrame, expand='table').value
                df_cen = self._leer_parquet('iplp_centrales.parquet')                
                Pobr = Pobr.merge(df_dict, 
                                  left_on='CENTRAL',
                                  right_on='CenNom')
                Pobr = Pobr.merge(df_cen, 
                                  left_on='CENTRAL',
                                  right_on='CENTRALES')
                
                Pobr.FINAL = Pobr.FINAL + pd.DateOffset(months=1)
                Pobr = Pobr.groupby(
                    ['CENTRAL', 'FINAL', 'Sub_Tipo']).agg({'Máxima.1': sum})
                
                
                col_index = True
                Pobr = pd.pivot_table(
                            data=Pobr, 
                            columns='FINAL',
                            index=['CENTRAL', 'Sub_Tipo'],
                            values='Máxima.1'
                            )
                Pobr = Pobr.ffill(axis=1)
            self.hoja.range('B16').options(index=col_index,
                                              expand='table').value = Pobr           
            self.mensajes('Término de proceso', self.hoja)

            # -- Exportando archivo csv filtrado
            if self.grabar != 'no':
                self.mensajes('Exportando Archivo', self.hoja)
                self._grabar_df(Pobr, nom_ar, formato=self.grabar, index=True)                

        except Exception as _error:
            self.mensajes(f'error desconocido: {_error}', self.hoja, 
                          sigue=False)
