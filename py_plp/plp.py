
import xlwings as xw
import pandas as pd
import numpy as np
from pathlib import Path
import time


class PyPlp:
	def __init__(self):
		self.wb         = xw.Book.caller()
		self.sh_etapas  = self.wb.sheets('Etapas')
		self.sh_ctrl    = self.wb.sheets('Control')
		self.sh_CMg     = self.wb.sheets('CMg')
		self.sh_Gen     = self.wb.sheets('Generacion')
		self.sh_Lin     = self.wb.sheets('Lineas')
		self.sh_CVar    = self.wb.sheets('CVar')
		self.sh_PObr    = self.wb.sheets('PlanObras')
		self.sh_emb     = self.wb.sheets('Embalses')
		self.sh_ret     = self.wb.sheets('Retiros x Barra')
		self.sh_dic_gen = self.wb.sheets('dic_gen')
		self.iplp		= self.sh_ctrl.range('D5').value
		self.path       = Path(self.wb.fullname).parent
		
		self.c_in = Path.joinpath(self.path, self.sh_ctrl.range('D3').value)
		self.c_out = Path.joinpath(self.path, self.sh_ctrl.range('D4').value)
		Path(self.c_in).mkdir(parents=True, exist_ok=True)
		Path(self.c_out).mkdir(parents=True, exist_ok=True)		
		self.pos_msg = 3

	def _leer_salida(self, ar_pqt, ar, hoja, col='f'):
		try:
			if 'parquet' in ar_pqt:
				df = pd.read_parquet(Path.joinpath(self.c_in, ar_pqt))
			if 'cmg' in ar:
				df = _arregla_df(df, 'cmg')
				self._toParquet(df, ar, 'cmg')
				self._toParquet(df, 'ret.parquet', 'ret')
				df = df[['Bloque', 'Hidro', 'BarNom', 'CMgBar']]
				return df
			elif 'cen' in ar:
				df = _arregla_df(df, 'gen')
				self._toParquet(df, ar, 'cen')
				self._toParquet(df, 'iny.parquet', 'iny')
				df = df[['Bloque', 'Hidro', 'CenNom', 'CenEgen']]
				return df
			elif 'lin' in ar:
				df = _arregla_df(df, 'lin')
				self._toParquet(df, ar, 'lin')
				df = df[['Bloque', 'Hidro', 'LinNom', 'LinUso']]
				return df
			elif 'embalses' in ar:
				df = _arregla_df(df, 'emb')
				self._toParquet(df, ar, 'emb')
				df = df[['Bloque', 'Hidro', 'EmbNom', 'EmbPsom',
						'EmbVfin', 'EmbVini', 'EmbQgen', 'EmbPsom2']]
				return df
			elif 'iplp' in ar:
				# Se aprovecha de sacar la info de costos variables,
				# Plan de obras, generadores, etapas, etc, desde la iplp
				xls = pd.ExcelFile(Path.joinpath(self.c_in, ar_pqt))
				df = pd.read_excel(xls, sheet_name='CVariable', header=4,
                       usecols='B:E')
				self._toParquet(df, 'iplp_cvar.parquet', 'otro')
				df = pd.read_excel(xls, sheet_name='Plan de Obras(5)', header=4,
                       usecols='B:F')
				self._toParquet(df, 'iplp_pobras.parquet', 'otro')
				df = pd.read_excel(xls, sheet_name='Centrales', header=4,
                       usecols='B:AS')
				df = df.loc[~df['CENTRALES'].str.contains('FALLA')]
				self._toParquet(df, 'iplp_centrales.parquet', 'otro')
				df = pd.read_excel(xls, sheet_name='Etapas', header=3,
                       usecols='A:B')
				self._toParquet(df, 'iplp_etapas.parquet', 'otro')

		except Exception as f_error:
			self.mensajes(f'Archivo salida no encontrado: {str(f_error)}', 
						hoja, col=col)

	def reconoce_parq(self, nombre_parquet, origen, hoja, col='F'):
		if Path.joinpath(self.c_in, nombre_parquet).exists():
			self.mensajes(f'Abriendo {nombre_parquet}', hoja, col=col)
			df = pd.read_parquet(Path.joinpath(self.c_in, nombre_parquet))
		elif Path.joinpath(self.c_in, origen).exists():
			self.mensajes(f'Creando {nombre_parquet}', hoja, col=col)
			if 'iplp' in nombre_parquet:
				self._leer_salida(origen, nombre_parquet, hoja)
				df = self._leer_parquet(nombre_parquet)
			else:
				df = self._leer_salida(origen, nombre_parquet, hoja)
		else:
			self.mensajes(f'No se encuentra archivo {origen}',
                 		  hoja, col=col, sigue=False)
		return df

	def deja_listo_para_filtrar(self, seleccion):
		seleccion = str(int(seleccion)) if isinstance(seleccion, float) \
  										else seleccion
		if ',' in seleccion:
			nom_ar = seleccion.replace(', ', '_')
			seleccion = seleccion.replace(' ', '')
			seleccion  = seleccion.upper().split(',')
		else:
			nom_ar = seleccion
			seleccion = [seleccion.upper()]
		return seleccion, nom_ar

	def _toParquet(self, df, nombre_parq, tipo):
		if tipo == 'cmg':
			df = df[['Bloque', 'Hidro', 'BarNom', 'CMgBar']]
		elif tipo == 'ret':
			df = df[['Bloque', 'Hidro', 'BarNom', 'DemBarP', 'DemBarE',
			         'BarRetP', 'BarRetE']]
		elif tipo == 'cen':
			df = df[['Bloque', 'Hidro', 'CenNom', 'CenEgen']]
		elif tipo == 'iny':
			df = df[['Bloque', 'Hidro', 'BarNom', 'CenInyP', 'CenInyE']]
		elif tipo == 'sim_hid':
			df = df[['Bloque', 'Anno', 'Banda_hor', 'Duracion', 'Ano_hid',
			         'Mes_hid', 'Ano_cal', 'Mes_cal', 'Bloque_dia']]
		elif tipo == 'lin':
			df = df[['Bloque', 'Hidro', 'LinNom', 'LinUso']]

		df.to_parquet(Path.joinpath(self.c_in, nombre_parq), engine='pyarrow')

	def _leer_parquet(self, my_file):
		df = pd.read_parquet(
				Path.joinpath(self.c_in, my_file), 
				engine='pyarrow'
			)
		return df

	def _grabar_df(self, df, nombre, formato, index=False):
		opciones = {'index': index, 'encoding': 'latin'}
		nom = nombre + '.' + formato
		if formato == 'csv':
			df.to_csv(Path.joinpath(self.c_out, nom), sep=',', **opciones)
		elif formato == 'tsv':
			df.to_csv(Path.joinpath(self.c_out, nom), sep='\t', **opciones)
		elif formato == 'xlsx':
			df.to_excel(Path.joinpath(self.c_out, nom), **opciones)

	def mensajes(self, msg, hoja, sigue=True, col='F', tiempo=0):
		cel = col + str(self.pos_msg)
		mensaje = hoja.range(cel)
		if msg!='' and hoja != self.sh_ctrl:
			mensaje.value = msg
		if tiempo!=0:
			mensaje.offset(-1, 3).value = round(time.time() - tiempo, 3)
			self.pos_msg -= 1
		if sigue:
			self.pos_msg += 1
		else:
			exit()

	def _filtra_df(self, df, col_name, name, sims, hoja):
		df, simuls = _arregla_df(df, 'pre')
		name = str(int(name)) if isinstance(name, float) else name
		if ',' in name:
			name = name.replace(' ', '')
			name = name.split(',')
		else:
			name = [name]
		
		nom = '|'.join(name)
		df = df.loc[df[col_name].str.contains(nom)]
		df = self._traductor_etapas(df, simuls)
		if sims!='Todas':
			#sims debe ser un arreglo, ejemplo [43, 50, 60]
			df = df.loc[np.in1d(df['Hidro'], sims)]
		# Control de DataFrame Vacío
		if df.count()[0] == 0:
			if hoja == 'CMg':
				self.mensajes('Barra no Encontrada', 'CMg', sigue=False)
				pass
			elif hoja == 'Generacion':
				self.mensajes('Nombre no encontrado', 'Generacion', sigue=False)
		else:
			return df

	def _hid_eta_parquet(self):
		dic_hor2ban = {
			key: '1_noche' if key in range(1, 7) else '2_dia' if
			key in range(7, 21) else '3_tarde' for key in range(1, 25)}

		# Modificaciones de indhor.csv y etapas.csv		
		df_ind = self._leer_csv('indhor.parquet', 'ind')
		df_eta = self._leer_csv('etapas.parquet', 'eta')
		ar_etapa = Path.joinpath(self.c_in, 'etapa.csv')
		df_eta.to_csv(ar_etapa, encoding='latin')
		df_eta = pd.read_csv(
					   ar_etapa, 
                       encoding='latin', 
                       skipinitialspace=True,
                       index_col=False, engine='c'
                       )
		df_ind.rename(columns={'Año': 'Ano_cal', 'Mes': 'Mes_cal'},
		              inplace=True)
		df_eta.rename(columns={'#Bloque': 'Bloque', 'Mes': 'Mes_hid'},
		              inplace=True)

		df_eta['Tipo'] = pd.to_numeric(df_eta['Tipo'].str.replace('Eta', ''))

		# No bloques por etapa = 5
		no_blo_eta = sum((df_eta['Tipo'] == 1))
		df_eta.columns = df_eta.columns.str.replace(" ", "")

		# Cambiar nombre columnas Bloque y Mes
		agno_cal_ini = df_ind['Ano_cal'].iloc[0]
		mes_cal_ini = df_ind['Mes_cal'].iloc[0]
		agno_hid_ini = agno_cal_ini - 1 if mes_cal_ini <= 3 else agno_cal_ini
		df_eta['Ano_hid'] = df_eta['Anno'] + agno_hid_ini - 1

		# Crear columna Blo_dia, Mes_cal y Ano_cal
		df_eta['Bloque_dia'] = df_eta['Bloque'].mod(no_blo_eta)
		df_eta['Bloque_dia'] = np.where(df_eta['Bloque_dia'] == 0, no_blo_eta,
		                                df_eta['Bloque_dia'])
		df_eta['Mes_cal'] = np.where(df_eta['Mes_hid'] <= 9,
		                             df_eta['Mes_hid'] + 3,
		                             df_eta['Mes_hid'] - 9)
		df_eta['Ano_cal'] = np.where(df_eta['Mes_cal'] >= 4,
		                             df_eta['Ano_hid'],
		                             df_eta['Ano_hid'] + 1)

		df_ind.drop(columns=['Ano_cal', 'Mes_cal', 'Dia'], inplace=True)
		df_ind.drop_duplicates(subset=['Bloque'], keep='first', inplace=True)
		df_ind['Banda_hor'] = df_ind['Hora'].map(dic_hor2ban)
		df_ind.drop(columns=['Hora'], inplace=True)

		df_eta = df_eta.merge(df_ind, how='left', on='Bloque')
		self._toParquet(df_eta,
		                Path.joinpath(self.c_in, 'sim_hid.parquet'), 'sim_hid')
		return df_eta

	def _traductor_etapas(self, df, sims):
		tr_parquet = 'sim_hid.parquet'
		existe = Path(Path.joinpath(self.c_in, tr_parquet))

		if existe.is_file():
			df_HS = self._leer_parquet('sim_hid.parquet')
		else:
			df_HS = self._hid_eta_parquet()
		
		df_h = sim_to_hidro(df_HS['Anno'].tolist(), sims, max(df_HS['Bloque']))
		#df.to_csv(Path.joinpath(self.c_in, 'df.csv'))
		#df_h.to_csv(Path.joinpath(self.c_in, 'df_h.csv'))
		
		df = df.merge(df_h, how='left', on=['Sim', 'Bloque'])
		df = df.merge(df_HS, how='left', on='Bloque')

		return df

	def _leer_csv(self, ar_csv, in_out, header=0):
		opt = {'encoding':'latin', 'header': header}
		if in_out == 'cin':
			df = pd.read_csv(self.c_in / ar_csv, **opt)
		elif in_out == 'cout':
			df = pd.read_csv(self.c_out / ar_csv, **opt)
		return df


def sim_to_hidro(aux_ano, no_sim, no_blo_tot):
	# Listas auxiliares
	aux_eta, aux_sim, aux_hid = [], [], []

	# Ciclo de simulaciones
	hid_ini = 1
	for i_sim in range(no_sim):
		# Ciclo de etapas
		i_hid = hid_ini
		for i_eta in range(no_blo_tot):
			aux_eta.append(i_eta + 1)
			aux_sim.append(i_sim + 1)
			# Cambiar hidrologia con agno hidrologico
			if i_eta > 0:
				if aux_ano[i_eta] != aux_ano[i_eta - 1]:
					i_hid += 1
					if i_hid > no_sim:
						i_hid = 1
			aux_hid.append(i_hid)
		hid_ini += 1

	# Simulacion igual al promedio
	i_sim, i_hid = no_sim + 1, no_sim + 1

	for i_eta in range(no_blo_tot):
		aux_eta.append(i_eta + 1)
		aux_sim.append(i_sim)
		aux_hid.append(i_hid)

	df_hid = pd.DataFrame(list(zip(aux_sim, aux_eta, aux_hid)),
	                      columns=['Sim', 'Bloque', 'Hidro'])
	return df_hid

def _arregla_df(df, tipo):    
    if tipo != 'pre':
        df['Hidro'] = df['Hidro'].str.rstrip()
        df['Hidro'] = df['Hidro'].str.replace("Sim", "")
        simuls = pd.to_numeric(df['Hidro'],
								'coerce').fillna(0).astype(np.int64).max()
        df.loc[df['Hidro'] == 'MEDIA', 'Hidro'] = simuls + 1
        df['Hidro'] = df['Hidro'].astype('int64')
        if tipo == 'cmg':
            df['BarNom'] = df['BarNom'].str.rstrip()
        elif tipo == 'gen':
            df['BarNom'] = df['BarNom'].str.rstrip()
            df['CenNom'] = df['CenNom'].str.rstrip()
        elif tipo == 'lin':
            df['LinNom'] = df['LinNom'].str.rstrip()
            df.loc[df['LinFluE'] < 0, 'LinUso'] *= -1
        elif tipo == 'emb':
            df['EmbNom'] = df['EmbNom'].str.rstrip()
        return df
    else:
        simuls = pd.to_numeric(df['Hidro'],
		                       'coerce').fillna(0).astype(np.int64).max()
        df.rename(columns={'Hidro': 'Sim'}, inplace=True)
        return df, simuls

