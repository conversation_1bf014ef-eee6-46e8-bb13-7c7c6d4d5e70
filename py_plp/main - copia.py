from py_plp import plp_cmg, plp_gen
import time

def main(tipo):
	t_ini = time.time()
	if tipo == 'cmg':
		cmg = plp_cmg.CmgPlp()
		try:
			cmg.cmg_plp()
		except Exception as cmg_error:
			cmg.mensajes('tiempo', sigue=True, msg='alo')
			cmg.mensajes('error desconocido', msg=cmg_error)

		menj = str(round(time.time() - t_ini, 2))
		cmg.mensajes('tiempo', sigue=True, msg=menj)
	elif tipo == 'gen':
		gen = plp_gen.GenPlp()
		try:
			gen.gen_plp()
		except Exception as gen_error:
			gen.mensajes('error desconocido', msg=gen_error, hoja='Generacion')

		menj = str(round(time.time() - t_ini, 2))
		gen.mensajes('tiempo', sigue=True, msg=menj, hoja='Generacion')
