from py_plp import plp_cmg, plp_gen, plp_lineas, plp_cvariable, plp_pobras
from py_plp import plp_iny_ret, carga_archivos, plp_embalses
import time

def main(tipo):
	t_ini = time.time()
	if tipo == 'cmg':
		cmg = plp_cmg.CmgPlp()
		try:
			cmg.cmg_plp()
		except Exception as cmg_error:
			cmg.mensajes(f'error desconocido: {cmg_error}', 
                cmg.hoja, sigue=False)

		menj = str(round(time.time() - t_ini, 2))
		cmg.mensajes(f'tiempo: {menj}', cmg.hoja)
	elif tipo == 'gen':
		gen = plp_gen.GenPlp()
		try:
			gen.gen_plp()
		except Exception as gen_error:
			gen.mensajes(f'error desconocido: {gen_error}', 
                gen.hoja, sigue=False)

		menj = str(round(time.time() - t_ini, 2))
		gen.mensajes(f'tiempo: {menj}', gen.hoja)
	elif tipo == 'lin':
		lin = plp_lineas.LinPlp()
		try:
			lin.lin_plp()
		except Exception as lin_error:
			lin.mensajes(f'error desconocido: {lin_error}', 
                lin.hoja, sigue=False)

		menj = str(round(time.time() - t_ini, 2))
		lin.mensajes(f'tiempo: {menj}', lin.hoja)
	elif tipo == 'cvar':
		cvar = plp_cvariable.CvarPlp()
		try:
			cvar.cvar_plp()
		except Exception as _error:
			cvar.mensajes(f'error desconocido: {_error}', 
                 cvar.hoja, sigue=False)

		menj = str(round(time.time() - t_ini, 2))
		cvar.mensajes(f'tiempo: {menj}', cvar.hoja)
	elif tipo == 'pobras':
		pob = plp_pobras.PoPlp()
		
		try:
			pob.pobras_plp()
		except Exception as _error:
			pob.mensajes(f'error desconocido: {_error}', pob.hoja, 
                sigue=False)

		menj = str(round(time.time() - t_ini, 2))
		pob.mensajes(f'tiempo: {menj}', pob.hoja)
	elif tipo == 'iny_ret':
		iny_ret = plp_iny_ret.InyRetPlp()
		
		try:
			iny_ret.retiros_plp()
		except Exception as _error:
			iny_ret.mensajes(f'error desconocido: {_error}', iny_ret.hoja, 
                sigue=False)

		menj = str(round(time.time() - t_ini, 2))
		iny_ret.mensajes(f'tiempo: {menj}', iny_ret.hoja)
	elif tipo == 'carga_etapa':
		etapas = carga_archivos.CargaArchivos()
		
		try:
			etapas.carga_etapas()
		except Exception as _error:
			etapas.mensajes(f'error desconocido: {_error}', etapas.hoja_m, 
                sigue=False, col='k')
	elif tipo == 'embalses':
		emb = plp_embalses.EmbalsesPLP()
		
		try:
			emb.emb_plp()
		except Exception as _error:
			emb.mensajes(f'error desconocido: {_error}', emb.hoja, 
                sigue=False)


#%%
if __name__ == '__main__':
	#%%

	gen = plp_gen.GenPlp()
	try:
		gen.gen_plp()
	except Exception as gen_error:
		gen.mensajes(f'error desconocido: {gen_error}', gen.hoja, sigue=False)

	menj = str(round(time.time() - t_ini, 2))
	gen.mensajes(f'tiempo: {menj}', gen.hoja)