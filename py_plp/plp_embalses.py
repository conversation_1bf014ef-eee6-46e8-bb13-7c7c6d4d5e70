from py_plp.plp import PyPlp
import pandas as pd
import time


class EmbalsesPLP(PyPlp):
	def __init__(self):
		super().__init__()
		self.hoja   	  = self.sh_emb
		self.emb          = self.hoja.range('D3').value
		self.tipo_res     = self.hoja.range('D4').value
		self.simulaciones = self.hoja.range('D5').value
		self.grabar       = self.hoja.range('D6').value
		self.hoja.range('B16').expand().value = None
		self.hoja.range('F3:i11').value = None

	def extrae_embalses(self):		
		plp_ar = 'embalses_sal.parquet'
		# reconoce primero si existe parquet, sino sale del programa
		df = self.reconoce_parq(plp_ar, 'plpemb.parquet', self.hoja)
		emb, nom_ar = self.deja_listo_para_filtrar(self.emb)
		
		self.mensajes('Filtrando embalses', self.hoja)
		#self.mensajes(df.columns, self.hoja)
		nom = '|'.join(emb)
		df['may'] = df['EmbNom'].str.upper()
		df = df.loc[df['may'].str.contains(nom)]

		df['EmbNom'] = df['EmbNom'].str.replace(' ', '')
		if self.simulaciones!='Todas':
			sim = self.simulaciones
			if isinstance(sim, str): 
				sim = sim.replace(' ', '')
				sim = sim.split(',')
				sim = [int(x) for x in sim]
			elif isinstance(sim, float):
				sim = [int(sim)]
			else:
				self.mensajes('Debe ingresar "Todas" o números en campo "Sims"', 
                              self.hoja, sigue=False)  
			df = df.loc[df['Hidro'].isin(sim)]
		return df, nom_ar

	def emb_plp(self):
		try:
			# -- Abriendo Archivos
			Emb, nom_ar = self.extrae_embalses()

			# -- Preparando Dataframes
			self.mensajes('Preparando archivos para mostrar', self.hoja)
			op = ''
			if self.tipo_res=='ExGen':
				op = 'EmbQgen'
			elif self.tipo_res=='V_ini':
				op = 'EmbVfin'
			elif self.tipo_res=='V_fin':
				op = 'EmbVini'
			elif self.tipo_res=='P_sombra':
				op = 'EmbPsom2'
			elif self.tipo_res=='P_USD/dm3':
				op = 'EmbPsom'
			emb = pd.pivot_table(
						data=Emb, 
						columns='Bloque',
						index=['Hidro', 'EmbNom'],
						values=op
						)
			self.hoja.range('B16').options(index=True,
			                                 expand='table').value = emb
			
			self.mensajes('Término de Proceso', self.hoja)
   
			# -- Exportando archivo csv filtrado
			if self.grabar != 'no':
				self.mensajes('Exportando archivo', self.hoja)
				self._grabar_df(emb, nom_ar, formato=self.grabar, index=True)				

		except Exception as _error:
			self.mensajes(f'error desconocido: {_error}', 
                 self.hoja, sigue=False)
