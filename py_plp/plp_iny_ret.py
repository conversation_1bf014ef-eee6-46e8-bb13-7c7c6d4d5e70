from py_plp.plp import PyPlp
import pandas as pd
import time


class InyRetPlp(PyPlp):
    def __init__(self):
        super().__init__()
        self.hoja         = self.sh_ret
        self.barra        = self.hoja.range('D3').value
        self.tipo_res     = self.hoja.range('D4').value
        self.simulaciones = self.hoja.range('D5').value
        self.grabar       = self.hoja.range('D6').value
        self.hoja.range('B16').expand().value = None
        self.hoja.range('F3:i11').value = None

    def extrae_retiros_iplp(self):
        plp_ret = 'ret.parquet'
        # reconoce primero si existe parquet, sino sale del programa
        df = self.reconoce_parq(plp_ret, 'plpbar.parquet', self.hoja)
        self.mensajes('', self.hoja, tiempo=time.time())
        ret, nom_ar = self.deja_listo_para_filtrar(self.barra)
                
        self.mensajes('Filtrando', self.hoja)
        nom = '|'.join(ret)
        df = df.loc[df['BarNom'].str.contains(nom)]
        if self.simulaciones!='Todas':
            sim = self.simulaciones
            if isinstance(sim, str): 
                sim = sim.replace(' ', '')
                sim = sim.split(',')
                sim = [int(x) for x in sim]
            elif isinstance(sim, float):
                sim = [int(sim)]
            else:
                self.mensajes('Debe ingresar "Todas" o números en campo "Sims"', 
                              self.hoja, sigue=False)  
            df = df.loc[df['Hidro'].isin(sim)]
        return df, nom_ar
    
    def retiros_plp(self):
        try:
            ret, nom_ar = self.extrae_retiros_iplp()

            # -- Preparando Dataframes
            self.mensajes('Preparando Salida', self.hoja)
            ret = pd.pivot_table(
                        data=ret, 
                        columns='Bloque',
                        index=['Hidro', 'BarNom'],
                        values= 'DemBarE'               #'DemBarE', 'BarRetE'
                        )            

            self.hoja.range('B16').options(index=True,
                                           expand='table').value = ret
            self.mensajes('Término de proceso', self.hoja)

            # -- Exportando archivo csv filtrado
            if self.grabar != 'no':
                self.mensajes('Exportando Archivo', self.hoja)
                self._grabar_df(ret, nom_ar, formato=self.grabar, index=True)

        except Exception as _error:
            self.mensajes(f'error desconocido: {_error}', self.hoja, 
                          sigue=False)
