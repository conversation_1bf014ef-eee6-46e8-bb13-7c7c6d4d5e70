from py_plp.plp import PyPlp
import pandas as pd
import time


class CmgPlp(PyPlp):
	def __init__(self):
		super().__init__()
		self.hoja   	  = self.sh_CMg
		self.barra        = self.hoja.range('D3').value
		self.tipo_res     = self.hoja.range('D4').value
		self.simulaciones = self.hoja.range('D5').value
		self.grabar       = self.hoja.range('D6').value
		self.hoja.range('B16').expand().value = None
		self.hoja.range('F3:i11').value = None

	def extrae_barra_plpbar(self):
		plp_ar = 'cmg.parquet'
		# reconoce primero si existe parquet, sino sale del programa
		df = self.reconoce_parq(plp_ar, 'plpbar.parquet', self.hoja)
		barra, nom_ar = self.deja_listo_para_filtrar(self.barra)
		
		self.mensajes('Filtrando barras', self.hoja)
		nom = '|'.join(barra)
		df['may'] = df['BarNom'].str.upper()
		df = df.loc[df['may'].str.contains(nom)]

		df['BarNom'] = df['BarNom'].str.replace(' ', '')
		if self.simulaciones!='Todas':
			sim = self.simulaciones
			if isinstance(sim, str): 
				sim = sim.replace(' ', '')
				if '-' in sim:
					s1 = sim.split('-')[0]
					s2 = sim.split('-')[1]
					try:
						df = df.loc[df['Hidro'].between(int(s1), int(s2))]
						return df, nom_ar
					except:
						self.mensajes(
							'Debe ingresar "Todas", números en campo "Sims", '
       						'o sims separada por "-"', 
							self.hoja, sigue=False)
				else:
					sim = sim.split(',')
					sim = [int(x) for x in sim]
			elif isinstance(sim, float):
				sim = [int(sim)]
			else:
				self.mensajes('Debe ingresar "Todas" o números en campo "Sims"', 
                              self.hoja, sigue=False)  
			df = df.loc[df['Hidro'].isin(sim)]
		return df, nom_ar

	def cmg_plp(self):
		try:
			# -- Abriendo Archivos
			t1 = time.time()
			CMg, nom_ar = self.extrae_barra_plpbar()

			# -- Preparando Dataframes
			self.mensajes('Preparando archivos para cálculo', self.hoja)
			t1 = time.time()
			cmg = pd.pivot_table(
						data=CMg, 
						columns='Bloque',
						index=['Hidro', 'BarNom'],
						values='CMgBar'
						)
			self.hoja.range('B16').options(index=True,
			                                 expand='table').value = cmg
			
			self.mensajes('Término de Proceso', self.hoja)
   
			# -- Exportando archivo csv filtrado
			if self.grabar != 'no':
				self.mensajes('Exportando archivo', self.hoja)
				self._grabar_df(cmg, nom_ar, formato=self.grabar, index=True)				

		except Exception as _error:
			self.mensajes(f'error desconocido: {_error}', 
                 self.hoja, sigue=False)

def calcP_med(df_sol, hidro='calendario', banda=False):
	agno, mes = ('Ano_cal', 'Mes_cal') if hidro == 'calendario' \
		else ('Ano_hid', 'Mes_hid')
	cols = ['Hidro', 'BarNom', agno, mes]
	ind = ['BarNom', agno, mes]

	if banda:
		ind.append('Banda_hor')
		cols.append('Banda_hor')

	df_sol['cmg_x_hr'] = df_sol['CMgBar'] * df_sol['Duracion']
	df_sol = df_sol.groupby(cols, as_index=False).sum()

	df_sol['CMgBar'] = df_sol['cmg_x_hr'] / df_sol['Duracion']
	cols.append('CMgBar')

	df_sol = df_sol.pivot_table(index=ind, columns='Hidro', values='CMgBar')

	return df_sol
