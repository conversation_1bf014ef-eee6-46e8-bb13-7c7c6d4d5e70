from py_plp.plp import PyPlp
import pandas as pd
import time


class GenPlp(PyPlp):
    def __init__(self):
        super().__init__()
        self.hoja         = self.sh_Gen
        self.gen          = self.hoja.range('D3').value
        self.tipo_res     = self.hoja.range('D4').value
        self.simulaciones = self.hoja.range('D5').value
        self.grabar       = self.hoja.range('D6').value
        self.hoja.range('B16').expand().value = None
        self.hoja.range('F3:i11').value = None

    def extrae_gen_plpcen(self):
        plp_ar = 'cen.parquet'
        # reconoce primero si existe parquet, sino sale del programa
        df = self.reconoce_parq(plp_ar, 'plpcen.parquet', self.hoja)
        gen, nom_ar = self.deja_listo_para_filtrar(self.gen)
        
        self.mensajes('Filtrando generadores', self.hoja)
        nom = '|'.join(gen)
        df = df.loc[df['CenNom'].str.contains(nom)]
        if df.count()[0] == 0:
            self.mensajes('Generador no encontrado', self.hoja, sigue=False)
    
        df['CenNom'] = df['CenNom'].str.replace(' ', '')
        if self.simulaciones!='Todas':
            sim = self.simulaciones
            if isinstance(sim, str): 
                sim = sim.replace(' ', '')
                if '-' in sim:
                    s1 = sim.split('-')[0]
                    s2 = sim.split('-')[1]
                    try:
                        df = df.loc[df['Hidro'].between(int(s1), int(s2))]
                        return df, nom_ar
                    except:
                        self.mensajes(
                            'Debe ingresar "Todas", números en campo "Sims", '
                            'o sims separada por "-"', 
                            self.hoja, sigue=False)
                else:
                    sim = sim.split(',')
                    sim = [int(x) for x in sim]                
                sim = sim.split(',')
                sim = [int(x) for x in sim]
            elif isinstance(sim, float):
                sim = [int(sim)]
            else:
                self.mensajes('Debe ingresar "Todas" o números en campo "Sims"', 
                              self.hoja, sigue=False)
            df = df.loc[df['Hidro'].isin(sim)]
        return df, nom_ar

    def gen_plp(self):
        try:
            # -- Abriendo Archivos
            t1 = time.time()
            Gen, nom_ar = self.extrae_gen_plpcen()

            # -- Preparando Dataframes
            self.mensajes('Preparando Salida', self.hoja)
            
            # -- Calculando Precio Medio
            t1 = time.time()
            col_index = ['Hidro']
            
            df_dict = self.sh_dic_gen.range('A1').options(
                        pd.DataFrame,
                        expand='table').value
            Gen = Gen.merge(df_dict, on='CenNom')
            
            if self.tipo_res=='Tecnología':
                Gen = Gen.groupby(
                    ['Hidro', 'Bloque', 'Sub_Tipo']).agg({'CenEgen': sum}) 
                col_index = col_index + ['Sub_Tipo']
            elif self.tipo_res=='Mensual':
                col_index = col_index + ['CenNom', 'Sub_Tipo']
            gen = pd.pivot_table(
                        data=Gen, 
                        columns='Bloque',
                        index=col_index,
                        values='CenEgen'
                        )
            self.hoja.range('B16').options(index=True,
                                             expand='table').value = gen           
            self.mensajes('Término de proceso', self.hoja)

            # -- Exportando archivo csv filtrado
            if self.grabar != 'no':
                self.mensajes('Exportando Archivo', self.hoja)
                self._grabar_df(gen, nom_ar, formato=self.grabar, index=True)                

        except Exception as cmg_error:
            self.mensajes(f'error desconocido: {cmg_error}', self.hoja, 
                          sigue=False)

def calcP_med(df_sol, hidro='calendario', banda=False):
    agno, mes = ('Ano_cal', 'Mes_cal') if hidro == 'calendario' \
        else ('Ano_hid', 'Mes_hid')
    cols = ['Hidro', 'CenNom', agno, mes]
    ind = ['CenNom', agno, mes]

    if banda:
        ind.append('Banda_hor')
        cols.append('Banda_hor')

    df_sol['gen_x_hr'] = df_sol['CenEgen'] * df_sol['Duracion']
    df_sol = df_sol.groupby(cols, as_index=False).sum()

    df_sol['CenEgen'] = df_sol['gen_x_hr'] / df_sol['Duracion']
    cols.append('CenEgen')
    df_sol = df_sol.pivot_table(index=ind, columns='Hidro',
                                values='CenEgen')

    return df_sol
